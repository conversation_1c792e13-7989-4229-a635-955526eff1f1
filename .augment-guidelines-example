# 耀森企业管理有限公司 - Augment智能体指南

## 🏢 项目信息
- 项目名称：耀森企业管理有限公司官网
- 技术栈：Next.js 15 + React 19 + TypeScript + Tailwind CSS
- 部署方式：Docker + 阿里云服务器

## 🎯 开发规范

### 代码风格
- 使用TypeScript严格模式
- 组件命名：PascalCase (UserProfile.tsx)
- 文件命名：kebab-case (user-profile.tsx)
- 变量命名：camelCase (userName)
- 常量命名：UPPER_SNAKE_CASE (API_ENDPOINTS)

### 组件开发
- 优先使用函数式组件和React Hooks
- 组件不超过200行代码
- 每个组件都要有TypeScript接口定义
- 使用React.memo优化性能
- 复杂状态使用useReducer

### 样式规范
- 使用Tailwind CSS 4.0
- 响应式设计：移动端优先
- 颜色方案：蓝色主色调，白色背景
- 遵循WCAG 2.1 AA无障碍标准

### 文件结构
```
src/
├── app/              # Next.js App Router
├── components/       # 可复用组件
├── lib/             # 工具函数
├── types/           # TypeScript类型
└── styles/          # 全局样式
```

## 🌐 国际化要求
- 界面语言：简体中文
- 注释语言：中文
- 错误信息：中文
- 文档编写：中文

## 🔒 安全要求
- 所有用户输入必须验证
- 使用环境变量管理敏感信息
- 实施CSRF和XSS防护
- API请求使用适当的认证

## 📦 依赖管理
- 包管理器：优先使用npm
- 避免安装不必要的依赖
- 定期更新依赖版本
- 使用package-lock.json锁定版本

## 🚀 部署规范
- 使用Docker容器化部署
- 官方Node.js镜像 + 目录映射
- 环境变量通过.env文件管理
- 生产环境启用压缩和缓存

## 💡 代码注释规范
- 复杂逻辑必须添加中文注释
- 函数和组件添加JSDoc注释
- TODO注释包含负责人和时间
- 临时代码添加FIXME标记

## 🧪 测试要求
- 重要功能必须有单元测试
- 使用Jest + React Testing Library
- 测试覆盖率不低于80%
- 关键路径需要端到端测试

## 📊 性能要求
- 首屏加载时间 < 3秒
- 交互响应时间 < 100ms
- Lighthouse性能评分 > 90
- 使用Next.js内置优化功能

## 🎯 业务逻辑
- 企业管理系统：注重数据安全和用户体验
- 表单验证：实时验证，友好错误提示
- 数据展示：支持分页、搜索、排序
- 权限控制：基于角色的访问控制

## 🔄 Git工作流
- 分支命名：feature/功能名、bugfix/问题描述
- 提交信息：使用中文，格式：类型(范围): 描述
- 代码审查：重要功能必须经过Code Review
- 版本标签：使用语义化版本号

## 🤖 AI助手行为指南

### 代码生成要求
- 生成的代码必须符合上述所有规范
- 包含完整的TypeScript类型定义
- 添加适当的错误处理
- 提供使用示例和说明

### 响应风格
- 使用中文回复
- 解释设计思路和技术选择
- 提供多种解决方案
- 包含最佳实践建议

### 问题解决
- 优先考虑项目的技术栈
- 提供完整的解决方案
- 包含测试代码
- 考虑性能和安全性

### 代码审查
- 检查是否符合项目规范
- 指出潜在的性能问题
- 建议改进方案
- 确保代码可维护性

## 📞 联系信息
- 公司：漯河耀森企业管理有限公司
- 技术支持：安徽追逐科技有限公司
- 备案号：豫ICP备2025123483号-1

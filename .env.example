# 安徽追逐科技有限公司官网 - Docker部署环境配置

# 基础配置
PROJECT_NAME=yaosen-website
COMPOSE_PROJECT_NAME=yaosen

# 网络配置
HTTP_PORT=80
HTTPS_PORT=443
NETWORK_SUBNET=172.20.0.0/16

# 域名配置（生产环境）
DOMAIN_NAME=your-domain.com
WWW_DOMAIN=www.your-domain.com

# SSL配置
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem

# 资源限制
NGINX_MEMORY_LIMIT=256M
NGINX_CPU_LIMIT=0.5
NGINX_MEMORY_RESERVATION=128M
NGINX_CPU_RESERVATION=0.25

# 日志配置
LOG_LEVEL=notice
ACCESS_LOG_PATH=./logs/nginx/access.log
ERROR_LOG_PATH=./logs/nginx/error.log

# 时区配置
TIMEZONE=Asia/Shanghai

# 健康检查配置
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3
HEALTH_CHECK_START_PERIOD=40s

# 备份配置
BACKUP_RETENTION_DAYS=30
BACKUP_DIR=./backups

# 安全配置
NGINX_WORKER_PROCESSES=auto
NGINX_WORKER_CONNECTIONS=1024
NGINX_CLIENT_MAX_BODY_SIZE=10M

# 缓存配置
STATIC_CACHE_DURATION=1y
HTML_CACHE_DURATION=1h

# 压缩配置
GZIP_ENABLED=on
GZIP_COMP_LEVEL=6
GZIP_MIN_LENGTH=1024

# 限流配置
RATE_LIMIT=10r/s
RATE_LIMIT_BURST=20
CONNECTION_LIMIT=10

# 监控配置
ENABLE_ACCESS_LOG=true
ENABLE_ERROR_LOG=true
LOG_FORMAT=main

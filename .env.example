# 安徽追逐科技有限公司官网 - Docker部署环境配置

# 🌍 环境配置
ENVIRONMENT=production
COMPOSE_PROJECT_NAME=yaosen

# 🌐 域名配置
DOMAIN_NAME=localhost
TLS_ENABLE=false
TRAEFIK_ENABLE=false

# 💾 资源配置 (可根据环境调整)
# 开发环境建议: MEMORY_LIMIT=256M, CPU_LIMIT=0.5
# 生产环境建议: MEMORY_LIMIT=512M, CPU_LIMIT=1.0
MEMORY_LIMIT=512M
CPU_LIMIT=1.0
MEMORY_RESERVATION=256M
CPU_RESERVATION=0.5

# 🏥 健康检查配置
HEALTH_INTERVAL=30s
HEALTH_TIMEOUT=10s
HEALTH_RETRIES=3
HEALTH_START_PERIOD=40s

# 🔒 安全配置
READ_ONLY_FS=true

# 📊 监控配置
LOG_LEVEL=notice
ENABLE_ACCESS_LOG=true
ENABLE_ERROR_LOG=true

# 🕐 时区配置
TZ=Asia/Shanghai

# 🔧 开发环境配置示例 (复制到 .env.dev)
# ENVIRONMENT=development
# MEMORY_LIMIT=256M
# CPU_LIMIT=0.5
# HEALTH_INTERVAL=60s
# READ_ONLY_FS=false
# DOMAIN_NAME=localhost

# 🚀 生产环境配置示例 (复制到 .env.prod)
# ENVIRONMENT=production
# MEMORY_LIMIT=1G
# CPU_LIMIT=2.0
# HEALTH_INTERVAL=15s
# READ_ONLY_FS=true
# DOMAIN_NAME=your-domain.com
# TLS_ENABLE=true
# TRAEFIK_ENABLE=true

# 🎨 在Augment中配置Figma MCP完整指南

## ✅ 好消息：Augment支持MCP！

根据官方文档，Augment Agent完全支持Model Context Protocol (MCP)服务器配置。

## 🚨 重要说明

**Figma MCP的特殊性**：
- Figma官方MCP服务器使用SSE (Server-Sent Events)协议
- 运行在 `http://127.0.0.1:3845/sse`
- 不是标准的命令行MCP服务器

## 🛠️ 配置方法

### 方法一：使用Augment设置面板

1. **打开Augment设置**
   - 点击Augment面板右上角的⚙️齿轮图标

2. **添加MCP服务器**
   - 在MCP服务器部分点击`+`按钮
   - 由于Figma MCP是SSE服务器，需要特殊配置

### 方法二：编辑settings.json（推荐）

1. **打开设置文件**
   ```
   Cmd/Ctrl + Shift + P → "Edit Settings" → "Edit in settings.json"
   ```

2. **添加配置**
   ```json
   {
     "augment.advanced": {
       "mcpServers": [
         {
           "name": "figma-mcp-proxy",
           "command": "npx",
           "args": ["@figma/mcp-server-proxy"],
           "env": {
             "FIGMA_MCP_URL": "http://127.0.0.1:3845/sse"
           }
         }
       ]
     }
   }
   ```

## 🔧 解决方案：创建MCP代理

由于Figma MCP使用SSE协议，我们需要创建一个代理来转换协议。

### 步骤1：安装依赖

```bash
npm install -g @modelcontextprotocol/sdk eventsource
```

### 步骤2：创建代理脚本

创建文件 `figma-mcp-proxy.js`：

```javascript
#!/usr/bin/env node

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const EventSource = require('eventsource');

class FigmaMCPProxy {
  constructor() {
    this.server = new Server(
      {
        name: 'figma-mcp-proxy',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );
    
    this.figmaUrl = 'http://127.0.0.1:3845/sse';
    this.setupHandlers();
  }

  setupHandlers() {
    // 列出可用工具
    this.server.setRequestHandler('tools/list', async () => {
      return {
        tools: [
          {
            name: 'get_figma_selection',
            description: 'Get currently selected Figma design elements',
            inputSchema: {
              type: 'object',
              properties: {},
            },
          },
          {
            name: 'get_figma_node',
            description: 'Get specific Figma node information',
            inputSchema: {
              type: 'object',
              properties: {
                nodeId: {
                  type: 'string',
                  description: 'Figma node ID',
                },
                fileKey: {
                  type: 'string',
                  description: 'Figma file key',
                },
              },
              required: ['nodeId', 'fileKey'],
            },
          },
          {
            name: 'generate_code_from_figma',
            description: 'Generate code from Figma design',
            inputSchema: {
              type: 'object',
              properties: {
                nodeId: {
                  type: 'string',
                  description: 'Figma node ID',
                },
                fileKey: {
                  type: 'string',
                  description: 'Figma file key',
                },
                framework: {
                  type: 'string',
                  description: 'Target framework (react, vue, angular)',
                  default: 'react',
                },
              },
              required: ['nodeId', 'fileKey'],
            },
          },
        ],
      };
    });

    // 调用工具
    this.server.setRequestHandler('tools/call', async (request) => {
      const { name, arguments: args } = request.params;
      
      try {
        const result = await this.callFigmaTool(name, args);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(result, null, 2),
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error calling Figma tool: ${error.message}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  async callFigmaTool(toolName, args) {
    // 这里实现与Figma MCP服务器的实际通信
    // 由于Figma MCP使用SSE，需要特殊处理
    
    switch (toolName) {
      case 'get_figma_selection':
        return await this.getFigmaSelection();
      case 'get_figma_node':
        return await this.getFigmaNode(args.nodeId, args.fileKey);
      case 'generate_code_from_figma':
        return await this.generateCodeFromFigma(args.nodeId, args.fileKey, args.framework);
      default:
        throw new Error(`Unknown tool: ${toolName}`);
    }
  }

  async getFigmaSelection() {
    // 实现获取当前选择的逻辑
    return {
      message: 'Getting current Figma selection...',
      selection: 'Current selection data would be here',
    };
  }

  async getFigmaNode(nodeId, fileKey) {
    // 实现获取特定节点的逻辑
    return {
      message: `Getting Figma node ${nodeId} from file ${fileKey}...`,
      nodeData: 'Node data would be here',
    };
  }

  async generateCodeFromFigma(nodeId, fileKey, framework = 'react') {
    // 实现代码生成的逻辑
    return {
      message: `Generating ${framework} code for node ${nodeId}...`,
      code: `// Generated ${framework} component\n// Code would be here`,
    };
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Figma MCP Proxy server running...');
  }
}

// 启动代理服务器
const proxy = new FigmaMCPProxy();
proxy.run().catch(console.error);
```

### 步骤3：配置Augment

在Augment的settings.json中添加：

```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "figma-mcp",
        "command": "node",
        "args": ["/path/to/figma-mcp-proxy.js"]
      }
    ]
  }
}
```

## 🎯 使用方法

配置完成后，在Augment Agent中可以这样使用：

```
请使用Figma MCP获取当前选择的设计元素信息
```

```
使用Figma MCP从节点ID "123:456" 生成React组件代码
```

## 🔍 验证配置

1. **重启VS Code**
2. **打开Augment Agent**
3. **检查MCP服务器状态**
4. **测试Figma工具调用**

## ⚠️ 注意事项

1. **Figma桌面应用必须运行**
2. **必须启用Dev Mode MCP Server**
3. **需要Professional/Organization/Enterprise计划**
4. **需要Dev或Full席位**

## 🐛 故障排除

### 连接问题
```bash
# 测试Figma MCP服务器
curl http://127.0.0.1:3845/sse
```

### 配置问题
- 检查settings.json语法
- 确认文件路径正确
- 重启VS Code

### 权限问题
- 确认Figma账户权限
- 检查文件访问权限

## 🎉 成功标志

配置成功后，您应该能够：
- ✅ 在Augment Agent中看到Figma工具
- ✅ 调用Figma MCP功能
- ✅ 获取设计信息和生成代码

## 📚 相关资源

- [Augment MCP文档](https://docs.augmentcode.com/setup-augment/mcp)
- [Figma MCP官方指南](https://help.figma.com/hc/en-us/articles/32132100833559)
- [Model Context Protocol](https://modelcontextprotocol.io/)

现在您可以在Augment中享受Figma MCP的强大功能了！🚀

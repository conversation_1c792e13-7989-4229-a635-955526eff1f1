# 🚀 安徽追逐科技有限公司官网 - 部署指南

## 📋 部署概述

这是一个纯静态HTML网站，可以部署到任何支持静态文件托管的服务器或平台。

## 🌐 部署选项

### 选项1：阿里云OSS静态网站托管（推荐）

```bash
# 1. 开启OSS静态网站功能
# 2. 上传所有文件到OSS Bucket
# 3. 配置默认首页为 index.html
# 4. 绑定自定义域名
# 5. 配置HTTPS证书
```

**优势**：
- 成本低廉
- 全球CDN加速
- 高可用性
- 自动HTTPS

### 选项2：传统Web服务器

#### Nginx配置示例
```nginx
server {
    listen 80;
    listen 443 ssl;
    server_name your-domain.com;
    
    # SSL配置
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    # 网站根目录
    root /var/www/yaosen-website;
    index index.html;
    
    # 静态文件缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # HTML文件缓存
    location ~* \.html$ {
        expires 1h;
        add_header Cache-Control "public";
    }
    
    # 安全头
    add_header X-Frame-Options "DENY";
    add_header X-Content-Type-Options "nosniff";
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
}
```

#### Apache配置示例
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/yaosen-website
    
    # 重定向到HTTPS
    Redirect permanent / https://your-domain.com/
</VirtualHost>

<VirtualHost *:443>
    ServerName your-domain.com
    DocumentRoot /var/www/yaosen-website
    
    # SSL配置
    SSLEngine on
    SSLCertificateFile /path/to/cert.pem
    SSLCertificateKeyFile /path/to/key.pem
    
    # 静态文件缓存
    <LocationMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
        Header set Cache-Control "public, immutable"
    </LocationMatch>
    
    # 安全头
    Header always set X-Frame-Options "DENY"
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
</VirtualHost>
```

### 选项3：免费托管平台

#### GitHub Pages
```bash
# 1. 创建GitHub仓库
# 2. 上传所有文件
# 3. 在Settings中启用Pages
# 4. 选择main分支作为源
```

#### Vercel
```bash
# 1. 连接GitHub仓库
# 2. 自动检测为静态网站
# 3. 一键部署
```

#### Netlify
```bash
# 1. 拖拽文件夹到Netlify
# 2. 或连接Git仓库
# 3. 自动部署
```

## 🔧 部署脚本

### 自动部署脚本（deploy.sh）
```bash
#!/bin/bash

# 部署配置
REMOTE_HOST="your-server-ip"
REMOTE_USER="root"
REMOTE_PATH="/var/www/yaosen-website"
LOCAL_PATH="."

echo "🚀 开始部署安徽追逐科技官网..."

# 检查必要文件
if [ ! -f "index.html" ]; then
    echo "❌ 错误: index.html 文件不存在"
    exit 1
fi

# 同步文件到服务器
echo "📁 同步文件到服务器..."
rsync -avz --progress \
    --exclude=".git" \
    --exclude="*.md" \
    --exclude="start-server.py" \
    $LOCAL_PATH/ $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/

# 设置文件权限
echo "🔒 设置文件权限..."
ssh $REMOTE_USER@$REMOTE_HOST "chmod -R 644 $REMOTE_PATH/*"
ssh $REMOTE_USER@$REMOTE_HOST "find $REMOTE_PATH -type d -exec chmod 755 {} \;"

# 重启Web服务器
echo "🔄 重启Web服务器..."
ssh $REMOTE_USER@$REMOTE_HOST "systemctl reload nginx"

echo "✅ 部署完成!"
echo "🌐 网站地址: https://your-domain.com"
```

## 🔍 部署验证

### 功能检查清单
- [ ] 网站可以正常访问
- [ ] 所有页面链接正常
- [ ] 响应式设计在移动端正常
- [ ] 表单提交功能正常
- [ ] 所有静态资源加载正常
- [ ] 版权信息显示正确

### 性能检查
```bash
# 使用curl测试响应时间
curl -w "@curl-format.txt" -o /dev/null -s "https://your-domain.com"

# 使用PageSpeed Insights测试
# 访问: https://pagespeed.web.dev/
```

### 安全检查
```bash
# 检查HTTPS配置
curl -I https://your-domain.com

# 检查安全头
curl -I https://your-domain.com | grep -E "(X-Frame-Options|X-Content-Type-Options|Strict-Transport-Security)"
```

## 📊 监控和维护

### 日志监控
```bash
# Nginx访问日志
tail -f /var/log/nginx/access.log

# Nginx错误日志
tail -f /var/log/nginx/error.log
```

### 性能监控
- 使用Google Analytics监控访问量
- 使用Google Search Console监控SEO表现
- 定期检查网站加载速度

### 备份策略
```bash
# 每日备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d)
tar -czf /backup/yaosen-website-$DATE.tar.gz /var/www/yaosen-website
find /backup -name "yaosen-website-*.tar.gz" -mtime +30 -delete
```

## 🆘 故障排除

### 常见问题

**问题1：网站无法访问**
```bash
# 检查Web服务器状态
systemctl status nginx

# 检查端口是否开放
netstat -tlnp | grep :80
netstat -tlnp | grep :443
```

**问题2：样式或脚本无法加载**
```bash
# 检查文件权限
ls -la /var/www/yaosen-website/

# 检查Nginx配置
nginx -t
```

**问题3：HTTPS证书问题**
```bash
# 检查证书有效期
openssl x509 -in /path/to/cert.pem -text -noout | grep "Not After"

# 更新Let's Encrypt证书
certbot renew
```

## 📞 技术支持

- **公司**: 安徽追逐科技有限公司
- **电话**: 400-123-4567
- **邮箱**: <EMAIL>
- **技术支持**: <EMAIL>

---

**部署成功后，您的网站将以极快的速度为用户提供服务！** 🚀

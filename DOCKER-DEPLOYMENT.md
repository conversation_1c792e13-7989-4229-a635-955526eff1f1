# 🐳 安徽追逐科技官网 - Docker部署指南

## 📋 部署架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   用户访问      │───▶│  Nginx容器       │───▶│  静态文件       │
│   HTTP/HTTPS    │    │  (官方镜像)      │    │  (目录映射)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │  日志 & 监控     │
                       │  健康检查        │
                       └──────────────────┘
```

## 🚀 快速部署

### 1. 环境准备

```bash
# 安装Docker和Docker Compose
curl -fsSL https://get.docker.com | sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 项目部署

```bash
# 克隆项目
git clone <repository-url>
cd yaosen-website

# 一键部署
./deploy.sh

# 检查状态
./deploy.sh status
```

### 3. 验证部署

```bash
# 检查容器状态
docker ps

# 检查网站访问
curl http://localhost/
curl http://localhost/health

# 查看日志
docker-compose logs -f
```

## 📁 项目结构

```
yaosen-website/
├── docker-compose.yml          # Docker编排配置
├── docker-compose.prod.yml     # 生产环境配置
├── nginx/
│   ├── nginx.conf             # Nginx主配置
│   └── conf.d/
│       └── yaosen-website.conf # 站点配置
├── logs/                      # 日志目录
├── ssl/                       # SSL证书目录
├── deploy.sh                  # 部署脚本
├── monitor.sh                 # 监控脚本
├── index.html                 # 网站文件
├── styles/                    # 样式文件
└── scripts/                   # 脚本文件
```

## ⚙️ 配置说明

### Docker Compose配置

**基础配置** (`docker-compose.yml`):
- 使用Nginx 1.25 Alpine镜像
- 目录映射：网站文件、配置、日志
- 健康检查：30秒间隔
- 资源限制：256M内存，0.5 CPU

**生产配置** (`docker-compose.prod.yml`):
- 更严格的资源限制
- 只读根文件系统
- 增强的安全配置
- 更频繁的健康检查

### Nginx配置特性

**性能优化**:
- Gzip压缩
- 静态文件缓存
- Keep-alive连接
- 工作进程优化

**安全配置**:
- 安全头设置
- 隐藏版本信息
- 访问限制
- 请求限流

**监控友好**:
- 详细访问日志
- 错误日志记录
- 健康检查端点

## 🔧 管理命令

### 部署脚本 (`deploy.sh`)

```bash
./deploy.sh deploy    # 部署服务（默认）
./deploy.sh stop      # 停止服务
./deploy.sh restart   # 重启服务
./deploy.sh logs      # 查看日志
./deploy.sh status    # 查看状态
./deploy.sh update    # 更新服务
./deploy.sh backup    # 手动备份
./deploy.sh help      # 显示帮助
```

### 监控脚本 (`monitor.sh`)

```bash
./monitor.sh monitor  # 执行完整监控检查
./monitor.sh report   # 生成监控报告
./monitor.sh cleanup  # 清理日志文件
./monitor.sh status   # 检查当前状态
./monitor.sh repair   # 尝试自动修复
./monitor.sh help     # 显示帮助
```

## 🔍 监控和维护

### 健康检查

系统提供多层健康检查：

1. **Docker健康检查**：容器级别的健康状态
2. **HTTP健康检查**：`/health` 端点检查
3. **监控脚本**：全面的系统监控

### 日志管理

```bash
# 查看实时日志
docker-compose logs -f

# 查看访问日志
tail -f logs/nginx/access.log

# 查看错误日志
tail -f logs/nginx/error.log

# 日志轮转（自动）
# - 超过30天的日志自动删除
# - 超过7天的日志自动压缩
```

### 备份策略

```bash
# 自动备份（部署时）
# - 备份网站文件
# - 备份配置文件
# - 保留最近10个备份

# 手动备份
./deploy.sh backup
```

## 🚨 故障排除

### 常见问题

**1. 容器启动失败**
```bash
# 检查配置文件
docker run --rm -v $(pwd)/nginx/nginx.conf:/etc/nginx/nginx.conf:ro nginx:1.25-alpine nginx -t

# 查看详细错误
docker-compose logs
```

**2. 网站无法访问**
```bash
# 检查端口占用
netstat -tlnp | grep :80

# 检查防火墙
sudo ufw status
```

**3. 性能问题**
```bash
# 检查资源使用
docker stats

# 检查系统负载
top
htop
```

### 紧急恢复

```bash
# 快速重启
docker-compose restart

# 完全重新部署
docker-compose down
docker-compose up -d

# 回滚到备份
cp -r backups/backup-YYYYMMDD-HHMMSS/* .
docker-compose restart
```

## 🔒 安全配置

### SSL/HTTPS配置

1. **获取SSL证书**：
```bash
# 使用Let's Encrypt
certbot certonly --webroot -w /usr/share/nginx/html -d your-domain.com
```

2. **配置SSL**：
```bash
# 复制证书到ssl目录
cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ssl/cert.pem
cp /etc/letsencrypt/live/your-domain.com/privkey.pem ssl/key.pem

# 启用HTTPS配置
# 编辑 nginx/conf.d/yaosen-website.conf
# 取消注释HTTPS服务器配置
```

### 防火墙配置

```bash
# 开放必要端口
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

## 📊 性能优化

### 缓存策略

- **静态资源**：1年缓存
- **HTML文件**：1小时缓存
- **API响应**：根据需要配置

### 压缩配置

- **Gzip压缩**：文本文件压缩率60%+
- **Brotli压缩**：更高压缩率（可选）

### CDN集成

```bash
# 配置CDN（可选）
# 1. 将静态资源上传到CDN
# 2. 修改HTML中的资源链接
# 3. 配置CDN回源
```

## 📈 扩展部署

### 负载均衡

```yaml
# docker-compose.scale.yml
version: '3.8'
services:
  yaosen-website:
    scale: 3  # 运行3个实例
  
  nginx-lb:
    image: nginx:1.25-alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx-lb.conf:/etc/nginx/nginx.conf:ro
```

### 高可用部署

```bash
# 多节点部署
# 1. 使用Docker Swarm或Kubernetes
# 2. 配置共享存储
# 3. 设置负载均衡器
# 4. 配置健康检查
```

---

## 📞 技术支持

- **公司**: 安徽追逐科技有限公司
- **技术支持**: <EMAIL>
- **紧急联系**: 400-123-4567

**部署成功后，您的网站将以极高的性能和可靠性为用户提供服务！** 🚀

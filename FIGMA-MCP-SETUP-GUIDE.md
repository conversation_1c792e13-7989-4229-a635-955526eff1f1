# 🎨 Figma MCP 完整安装配置指南

## 📋 概述

Figma MCP (Model Context Protocol) 是Figma官方提供的服务器，允许AI工具直接访问和处理Figma设计文件。

## 🎯 功能特性

- ✅ 从Figma框架生成代码
- ✅ 提取设计上下文（变量、组件、布局）
- ✅ 与Code Connect集成
- ✅ 支持多种AI代码编辑器

## 📋 前置要求

### 系统要求
- **Figma账户**：Professional、Organization或Enterprise计划
- **Dev或Full席位**
- **Figma桌面应用**（必须，网页版不支持）
- **支持MCP的代码编辑器**

### 支持的编辑器
- VS Code (需要GitHub Copilot)
- Cursor
- Windsurf
- Claude Code

## 🚀 第一步：启用Figma MCP服务器

### 1. 安装Figma桌面应用
```bash
# 下载地址
https://www.figma.com/downloads/
```

### 2. 启用MCP服务器
1. 打开Figma桌面应用
2. 点击左上角 **Figma菜单**
3. 选择 **Preferences** → **Enable Dev Mode MCP Server**
4. 确认看到"服务器已启用"的消息

### 3. 确认服务器运行
- 服务器地址：`http://127.0.0.1:3845/sse`
- 保持Figma桌面应用运行状态

## ⚙️ 第二步：配置代码编辑器

### VS Code 配置

#### 前置要求
- 安装GitHub Copilot扩展
- 启用GitHub Copilot订阅

#### 配置步骤
1. 打开VS Code设置：`⌘ + ,` (Mac) 或 `Ctrl + ,` (Windows)
2. 搜索"MCP"
3. 点击"Edit in settings.json"
4. 添加以下配置：

```json
{
  "chat.mcp.discovery.enabled": true,
  "mcp": {
    "servers": {
      "Figma Dev Mode MCP": {
        "type": "sse",
        "url": "http://127.0.0.1:3845/sse"
      }
    }
  },
  "chat.agent.enabled": true
}
```

5. 重启VS Code
6. 打开聊天工具栏：`⌥⌘B` (Mac) 或 `Alt+Ctrl+B` (Windows)
7. 切换到**Agent**模式
8. 查看MCP工具是否可用

### Cursor 配置

#### 配置步骤
1. 打开 **Cursor → Settings → Cursor Settings**
2. 转到 **MCP** 标签页
3. 点击 **+ Add new global MCP server**
4. 输入以下配置：

```json
{
  "mcpServers": {
    "Figma": {
      "url": "http://127.0.0.1:3845/sse"
    }
  }
}
```

5. 保存配置
6. 重启Cursor

### Windsurf 配置

#### 配置步骤
1. 打开 **Windsurf → Settings → Windsurf Settings** 或 `⌘ + ,`
2. 导航到 **Cascade settings**
3. 选择 **Open plugin store**
4. 搜索 **Figma** 并安装插件
5. 打开 **Cascade**，确认看到Figma MCP服务器

**注意**：Windsurf配置中使用`serverUrl`而不是`url`

### Claude Code 配置

#### 配置步骤
1. 打开终端
2. 运行以下命令：

```bash
# 添加Figma MCP服务器
claude mcp add --transport sse figma-dev-mode-mcp-server http://127.0.0.1:3845/sse

# 查看所有配置的服务器
claude mcp list

# 获取特定服务器详情
claude mcp get figma-dev-mode-mcp-server

# 如需移除服务器
claude mcp remove figma-dev-mode-mcp-server
```

## 🎯 第三步：使用Figma MCP

### 方式一：基于选择的使用

1. **在Figma中选择框架或图层**
   - 打开Figma桌面应用
   - 选择要转换为代码的框架或组件

2. **在代码编辑器中提示AI**
   ```
   请帮我实现当前在Figma中选择的设计
   ```

### 方式二：基于链接的使用

1. **复制Figma链接**
   - 在Figma中右键点击框架或图层
   - 选择"Copy link"

2. **在代码编辑器中提示AI**
   ```
   请帮我实现这个Figma设计：[粘贴链接]
   ```

## 🔧 故障排除

### 常见问题

#### 1. 连接失败
**解决方案：**
- 确保Figma桌面应用正在运行
- 检查MCP服务器是否已启用
- 重启Figma桌面应用和代码编辑器

#### 2. 找不到MCP工具
**解决方案：**
- 验证配置文件语法正确
- 确认服务器地址：`http://127.0.0.1:3845/sse`
- 重启相关应用程序

#### 3. VS Code中无法使用
**解决方案：**
- 确认已安装并启用GitHub Copilot
- 检查Copilot订阅状态
- 确认在Agent模式下使用

#### 4. 权限问题
**解决方案：**
- 确认Figma账户有Dev或Full席位
- 验证计划类型（Professional/Organization/Enterprise）
- 检查文件访问权限

### 验证安装

#### 检查服务器状态
```bash
# 测试服务器连接
curl http://127.0.0.1:3845/sse
```

#### 检查编辑器配置
- VS Code：查看MCP工具列表
- Cursor：检查MCP服务器状态
- Windsurf：确认插件已安装
- Claude Code：运行`claude mcp list`

## 📚 最佳实践

### 1. 设计准备
- 确保Figma设计结构清晰
- 使用有意义的图层命名
- 组织好组件和变量

### 2. 代码生成
- 提供清晰的上下文描述
- 指定目标技术栈
- 说明特殊要求或约束

### 3. 工作流程
- 保持Figma桌面应用运行
- 定期更新设计文件
- 使用版本控制管理生成的代码

## 🔗 相关资源

- [Figma官方MCP文档](https://help.figma.com/hc/en-us/articles/32132100833559)
- [Model Context Protocol官网](https://modelcontextprotocol.io/)
- [VS Code MCP文档](https://code.visualstudio.com/docs/copilot/chat/mcp-servers)
- [Cursor MCP文档](https://docs.cursor.com/context/model-context-protocol)

## 🎉 开始使用

配置完成后，您就可以：

1. **选择Figma设计** → **生成对应代码**
2. **提取设计系统** → **保持代码一致性**
3. **快速原型开发** → **提高开发效率**

享受Figma与AI代码生成的无缝集成体验！

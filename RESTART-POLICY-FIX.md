# 🔧 Docker Compose重启策略修复

## 🚨 问题分析

### 错误信息
```
Error response from daemon: invalid restart policy: maximum retry count can only be used with 'on-failure'
```

### 根本原因
Docker Compose配置中存在**重启策略冲突**：

1. **容器级重启策略**: `restart: unless-stopped`
2. **部署级重启策略**: `restart_policy` with `max_attempts`

这两种策略不能同时使用，且 `max_attempts` 只能与 `on-failure` 策略配合使用。

## ✅ 修复方案

### 1. 移除过时的version属性

**修复前**:
```yaml
version: '3.8'
services:
```

**修复后**:
```yaml
services:
```

**原因**: Docker Compose新版本中，`version` 属性已过时。

### 2. 修复重启策略冲突

**修复前**:
```yaml
restart: unless-stopped
deploy:
  restart_policy:
    condition: any
    delay: 5s
    max_attempts: 3
    window: 120s
```

**修复后**:
```yaml
restart: unless-stopped
deploy:
  resources:
    limits:
      memory: ${MEMORY_LIMIT:-512M}
      cpus: ${CPU_LIMIT:-1.0}
    reservations:
      memory: ${MEMORY_RESERVATION:-256M}
      cpus: ${CPU_RESERVATION:-0.5}
```

**原因**: 
- 保留简单可靠的 `restart: unless-stopped`
- 移除冲突的 `restart_policy` 配置
- 保留资源限制配置

### 3. 简化网络和数据卷配置

**修复前**:
```yaml
networks:
  yaosen-network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: yaosen-br
    ipam:
      config:
        - subnet: **********/16
          gateway: **********

volumes:
  nginx-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./logs/nginx
```

**修复后**:
```yaml
networks:
  yaosen-network:
    driver: bridge

volumes:
  nginx-logs:
    driver: local
```

**原因**: 简化配置，避免不必要的复杂性。

## 🎯 重启策略说明

### Docker Compose中的重启策略类型

1. **容器级重启策略** (`restart`):
   - `no`: 不自动重启
   - `always`: 总是重启
   - `unless-stopped`: 除非手动停止，否则重启
   - `on-failure`: 仅在失败时重启

2. **部署级重启策略** (`deploy.restart_policy`):
   - 主要用于Docker Swarm模式
   - 在单机Docker Compose中通常不需要

### 推荐配置

对于静态网站项目，推荐使用：
```yaml
restart: unless-stopped
```

**优势**:
- 简单可靠
- 系统重启后自动启动
- 手动停止后不会自动重启
- 兼容性好

## 🚀 验证修复

### 1. 配置语法检查
```bash
docker-compose config
```

### 2. 启动测试
```bash
# 清理旧容器
docker-compose down

# 启动新配置
docker-compose up -d

# 检查状态
docker-compose ps
```

### 3. 重启策略测试
```bash
# 测试容器重启
docker restart yaosen-website

# 检查容器是否自动重启
docker-compose ps
```

## 📊 修复前后对比

| 配置项 | 修复前 | 修复后 | 改进 |
|--------|--------|--------|------|
| Version属性 | 过时警告 | 已移除 | 消除警告 |
| 重启策略 | 冲突错误 | 简化配置 | 修复错误 |
| 网络配置 | 复杂配置 | 简化配置 | 提升兼容性 |
| 数据卷配置 | 复杂绑定 | 简化配置 | 减少错误 |

## 💡 最佳实践

### 重启策略选择指南

```yaml
# 开发环境 - 手动控制
restart: "no"

# 测试环境 - 平衡控制
restart: unless-stopped

# 生产环境 - 高可用
restart: unless-stopped  # 或 always
```

### 配置简化原则

1. **够用即可**: 不追求过度复杂的配置
2. **兼容优先**: 选择兼容性最好的配置
3. **错误最少**: 减少配置错误的可能性
4. **维护简单**: 便于理解和维护

## 🔧 故障排除

### 常见问题

**问题1: 重启策略错误**
```bash
# 检查配置语法
docker-compose config

# 查看具体错误
docker-compose up --no-daemon
```

**问题2: 网络配置问题**
```bash
# 清理网络
docker network prune

# 重新创建
docker-compose up -d
```

**问题3: 数据卷问题**
```bash
# 检查目录权限
ls -la logs/nginx/

# 重新创建目录
mkdir -p logs/nginx
```

## 📋 修复检查清单

- [ ] 移除version属性
- [ ] 修复重启策略冲突
- [ ] 简化网络配置
- [ ] 简化数据卷配置
- [ ] 验证配置语法
- [ ] 测试容器启动
- [ ] 验证网站访问

---

**🎉 修复完成！现在Docker Compose应该可以正常启动了！**

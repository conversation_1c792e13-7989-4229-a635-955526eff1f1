# 🔒 安全修复方案

## 🎯 修复概述

基于安全审计报告，提供具体的修复代码和配置，确保网站安全性达到企业级标准。

## 🔴 高危问题修复

### 1. XSS漏洞修复 - scripts/main.js

**原有代码**:
```javascript
notification.innerHTML = `
    <div class="notification-content">
        <span class="notification-icon">${getNotificationIcon(type)}</span>
        <span class="notification-message">${message}</span>
        <button class="notification-close">&times;</button>
    </div>
`;
```

**修复后代码**:
```javascript
// 安全的DOM操作，防止XSS
const content = document.createElement('div');
content.className = 'notification-content';

const icon = document.createElement('span');
icon.className = 'notification-icon';
icon.textContent = getNotificationIcon(type);

const messageSpan = document.createElement('span');
messageSpan.className = 'notification-message';
messageSpan.textContent = message; // 使用textContent防止XSS

const closeBtn = document.createElement('button');
closeBtn.className = 'notification-close';
closeBtn.innerHTML = '&times;';

content.appendChild(icon);
content.appendChild(messageSpan);
content.appendChild(closeBtn);
notification.appendChild(content);
```

### 2. CSRF保护实现

**HTML表单修复**:
```html
<form action="#" method="POST" class="form" id="contactForm">
    <!-- CSRF令牌 -->
    <input type="hidden" name="csrf_token" id="csrfToken" value="">
    
    <!-- 其他表单字段保持不变 -->
    <div class="form-group">
        <label for="name">姓名 *</label>
        <input type="text" id="name" name="name" required maxlength="50">
    </div>
    <!-- ... -->
</form>
```

**JavaScript CSRF处理**:
```javascript
// 生成CSRF令牌
function generateCSRFToken() {
    return 'csrf_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
}

// 设置CSRF令牌
function setCSRFToken() {
    const token = generateCSRFToken();
    const csrfInput = document.getElementById('csrfToken');
    if (csrfInput) {
        csrfInput.value = token;
        // 存储到sessionStorage用于验证
        sessionStorage.setItem('csrf_token', token);
    }
}

// 验证CSRF令牌
function validateCSRFToken(formData) {
    const formToken = formData.get('csrf_token');
    const sessionToken = sessionStorage.getItem('csrf_token');
    return formToken === sessionToken && formToken !== '';
}
```

### 3. 容器安全配置修复

**docker-compose.yml修复**:
```yaml
services:
  yaosen-website:
    image: nginx:1.25-alpine
    container_name: yaosen-website
    restart: unless-stopped
    
    ports:
      - "80:80"
      - "443:443"
    
    volumes:
      # 只映射必要的网站文件，避免暴露敏感文件
      - ./index.html:/usr/share/nginx/html/index.html:ro
      - ./styles:/usr/share/nginx/html/styles:ro
      - ./scripts:/usr/share/nginx/html/scripts:ro
      - ./favicon.ico:/usr/share/nginx/html/favicon.ico:ro
      # 配置文件映射
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      # SSL证书映射
      - ./ssl:/etc/nginx/ssl:ro
      # 日志映射
      - ./logs/nginx:/var/log/nginx
    
    # 安全配置
    security_opt:
      - no-new-privileges:true
    user: "101:101"  # nginx用户
    
    # 只读根文件系统
    read_only: true
    tmpfs:
      - /var/cache/nginx:rw,noexec,nosuid,size=100m
      - /var/run:rw,noexec,nosuid,size=100m
      - /tmp:rw,noexec,nosuid,size=100m
```

## 🟡 中危问题修复

### 4. 内容安全策略 (CSP) 配置

**nginx/conf.d/yaosen-website.conf添加**:
```nginx
# 内容安全策略
add_header Content-Security-Policy "
    default-src 'self';
    script-src 'self' 'unsafe-inline';
    style-src 'self' 'unsafe-inline' fonts.googleapis.com;
    font-src 'self' fonts.gstatic.com;
    img-src 'self' data:;
    connect-src 'self';
    frame-ancestors 'none';
    base-uri 'self';
    form-action 'self';
" always;

# 权限策略
add_header Permissions-Policy "
    geolocation=(),
    microphone=(),
    camera=(),
    payment=(),
    usb=(),
    magnetometer=(),
    gyroscope=(),
    speaker=()
" always;
```

### 5. 输入验证增强

**JavaScript输入验证**:
```javascript
// 增强的输入验证
function validateInput(data) {
    const errors = [];
    
    // 姓名验证
    if (!data.name || data.name.trim().length < 2 || data.name.trim().length > 50) {
        errors.push('姓名长度必须在2-50个字符之间');
    }
    if (!/^[\u4e00-\u9fa5a-zA-Z\s]+$/.test(data.name.trim())) {
        errors.push('姓名只能包含中文、英文和空格');
    }
    
    // 邮箱验证
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!data.email || !emailRegex.test(data.email)) {
        errors.push('请输入有效的邮箱地址');
    }
    
    // 电话验证
    if (data.phone && !/^1[3-9]\d{9}$/.test(data.phone)) {
        errors.push('请输入有效的手机号码');
    }
    
    // 消息长度验证
    if (!data.message || data.message.trim().length < 10 || data.message.trim().length > 1000) {
        errors.push('详细需求长度必须在10-1000个字符之间');
    }
    
    return errors;
}

// 输入清理函数
function sanitizeInput(input) {
    if (typeof input !== 'string') return '';
    
    return input
        .trim()
        .replace(/[<>]/g, '') // 移除尖括号
        .replace(/javascript:/gi, '') // 移除javascript协议
        .replace(/on\w+=/gi, '') // 移除事件处理器
        .substring(0, 1000); // 限制长度
}
```

### 6. 子资源完整性 (SRI) 实现

**index.html修复**:
```html
<!-- 字体资源添加SRI -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" 
      rel="stylesheet"
      integrity="sha384-rbs5oq9XuKqUOmIkn7dd8W2+UZRZjZZjZZjZZjZZjZZjZZjZZjZZjZZjZZjZZjZ"
      crossorigin="anonymous">

<!-- 本地资源也可以添加完整性检查 -->
<link rel="stylesheet" href="styles/main.css" 
      integrity="sha384-[计算的哈希值]">
<script src="scripts/main.js" 
        integrity="sha384-[计算的哈希值]"></script>
```

## 🟢 低危问题修复

### 7. 安全的错误处理

**JavaScript错误处理**:
```javascript
// 安全的错误处理
function handleError(error, context = '') {
    // 记录错误到控制台（开发环境）
    if (process.env.NODE_ENV === 'development') {
        console.error(`Error in ${context}:`, error);
    }
    
    // 显示用户友好的错误信息
    const userMessage = getUserFriendlyErrorMessage(error);
    showNotification(userMessage, 'error');
    
    // 发送错误报告（生产环境）
    if (process.env.NODE_ENV === 'production') {
        sendErrorReport(error, context);
    }
}

function getUserFriendlyErrorMessage(error) {
    // 不暴露技术细节给用户
    const friendlyMessages = {
        'NetworkError': '网络连接异常，请检查网络后重试',
        'ValidationError': '输入信息有误，请检查后重新提交',
        'TimeoutError': '请求超时，请稍后重试',
        'default': '操作失败，请稍后重试或联系客服'
    };
    
    return friendlyMessages[error.name] || friendlyMessages.default;
}
```

### 8. 日志安全配置

**nginx/nginx.conf日志配置**:
```nginx
# 安全的日志格式，避免记录敏感信息
log_format secure '$remote_addr - $remote_user [$time_local] '
                  '"$request_method $scheme://$host$request_uri $server_protocol" '
                  '$status $body_bytes_sent "$http_referer" '
                  '"$http_user_agent" $request_time';

# 过滤敏感参数的日志格式
map $request_uri $loggable {
    ~*password 0;
    ~*token 0;
    ~*key 0;
    default 1;
}

access_log /var/log/nginx/access.log secure if=$loggable;
```

## 🔧 自动化安全检查脚本

**security-check.sh增强版**:
```bash
#!/bin/bash

# 安全检查脚本增强版
echo "🔒 执行安全检查..."

# 检查XSS防护
echo "🔍 检查XSS防护..."
if grep -r "innerHTML.*\${" scripts/; then
    echo "❌ 发现潜在XSS风险"
    exit 1
fi

# 检查CSRF保护
echo "🔍 检查CSRF保护..."
if ! grep -q "csrf_token" index.html; then
    echo "❌ 缺少CSRF保护"
    exit 1
fi

# 检查CSP配置
echo "🔍 检查CSP配置..."
if ! grep -q "Content-Security-Policy" nginx/conf.d/*.conf; then
    echo "❌ 缺少CSP配置"
    exit 1
fi

echo "✅ 安全检查通过"
```

## 📋 修复检查清单

### 🔴 高危修复验证
- [ ] XSS漏洞已修复（使用安全DOM操作）
- [ ] CSRF保护已实现（令牌验证）
- [ ] 容器文件映射已限制（只映射必要文件）

### 🟡 中危修复验证
- [ ] CSP头已配置（内容安全策略）
- [ ] 输入验证已增强（客户端+服务端）
- [ ] SRI检查已实现（子资源完整性）

### 🟢 低危修复验证
- [ ] 错误处理已优化（不泄露敏感信息）
- [ ] 日志配置已加固（过滤敏感参数）
- [ ] 安全检查脚本已部署

## 🚀 部署安全修复

```bash
# 1. 备份当前版本
./deploy.sh backup

# 2. 应用安全修复
# 更新相关文件...

# 3. 运行安全检查
./security-check.sh

# 4. 重新部署
./deploy.sh deploy

# 5. 验证修复效果
curl -I http://localhost/ | grep -E "(X-Frame-Options|Content-Security-Policy)"
```

---

**⚠️ 重要**: 请按优先级顺序实施修复，确保每个修复都经过测试验证！

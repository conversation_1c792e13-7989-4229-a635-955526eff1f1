{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "figma-dev-mode",
        "command": "node",
        "args": ["-e", "
          const http = require('http');
          const EventSource = require('eventsource');
          
          // Figma MCP代理服务器
          // 将SSE连接转换为标准MCP协议
          
          const FIGMA_MCP_URL = 'http://127.0.0.1:3845/sse';
          
          class FigmaMCPProxy {
            constructor() {
              this.eventSource = null;
              this.connected = false;
            }
            
            async connect() {
              try {
                this.eventSource = new EventSource(FIGMA_MCP_URL);
                
                this.eventSource.onopen = () => {
                  this.connected = true;
                  console.log('Connected to Figma MCP server');
                };
                
                this.eventSource.onerror = (error) => {
                  console.error('Figma MCP connection error:', error);
                  this.connected = false;
                };
                
                return true;
              } catch (error) {
                console.error('Failed to connect to Figma MCP:', error);
                return false;
              }
            }
            
            async handleRequest(request) {
              if (!this.connected) {
                await this.connect();
              }
              
              // 处理MCP请求并转发到Figma MCP服务器
              switch (request.method) {
                case 'tools/list':
                  return {
                    tools: [
                      {
                        name: 'get_figma_selection',
                        description: 'Get currently selected Figma design elements'
                      },
                      {
                        name: 'get_figma_node',
                        description: 'Get specific Figma node by ID'
                      },
                      {
                        name: 'generate_code_from_figma',
                        description: 'Generate code from Figma design'
                      }
                    ]
                  };
                  
                case 'tools/call':
                  return await this.callFigmaTool(request.params);
                  
                default:
                  throw new Error(`Unsupported method: ${request.method}`);
              }
            }
            
            async callFigmaTool(params) {
              // 实际调用Figma MCP工具的逻辑
              // 这里需要根据Figma MCP的具体API实现
              return {
                content: [
                  {
                    type: 'text',
                    text: `Called Figma tool: ${params.name} with args: ${JSON.stringify(params.arguments)}`
                  }
                ]
              };
            }
          }
          
          // 启动代理服务器
          const proxy = new FigmaMCPProxy();
          
          process.stdin.on('data', async (data) => {
            try {
              const request = JSON.parse(data.toString());
              const response = await proxy.handleRequest(request);
              
              console.log(JSON.stringify({
                jsonrpc: '2.0',
                id: request.id,
                result: response
              }));
            } catch (error) {
              console.log(JSON.stringify({
                jsonrpc: '2.0',
                id: request.id || null,
                error: {
                  code: -1,
                  message: error.message
                }
              }));
            }
          });
          
          // 初始化连接
          proxy.connect();
        "],
        "env": {
          "NODE_ENV": "production"
        }
      }
    ]
  }
}

#!/bin/bash

# 安徽追逐科技有限公司官网 - Docker部署脚本
# 使用Nginx官方镜像 + 目录映射部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="yaosen-website"
COMPOSE_FILE="docker-compose.yml"
BACKUP_DIR="./backups"
LOG_DIR="./logs"

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：检查Docker和Docker Compose
check_requirements() {
    print_message $BLUE "🔍 检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        print_message $RED "❌ Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_message $RED "❌ Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    print_message $GREEN "✅ Docker 和 Docker Compose 已安装"
}

# 函数：创建必要的目录
create_directories() {
    print_message $BLUE "📁 创建必要的目录..."
    
    mkdir -p $LOG_DIR/nginx
    mkdir -p $BACKUP_DIR
    mkdir -p ssl
    
    # 设置日志目录权限
    chmod 755 $LOG_DIR
    chmod 755 $LOG_DIR/nginx
    
    print_message $GREEN "✅ 目录创建完成"
}

# 函数：验证配置文件
validate_config() {
    print_message $BLUE "🔧 验证配置文件..."
    
    # 检查必要文件
    local required_files=(
        "index.html"
        "styles/main.css"
        "scripts/main.js"
        "nginx/nginx.conf"
        "nginx/conf.d/yaosen-website.conf"
        "$COMPOSE_FILE"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            print_message $RED "❌ 缺少必要文件: $file"
            exit 1
        fi
    done
    
    # 验证Nginx配置
    print_message $BLUE "🔧 验证Nginx配置..."
    docker run --rm -v $(pwd)/nginx/nginx.conf:/etc/nginx/nginx.conf:ro \
        -v $(pwd)/nginx/conf.d:/etc/nginx/conf.d:ro \
        nginx:1.25-alpine nginx -t
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ Nginx配置验证通过"
    else
        print_message $RED "❌ Nginx配置验证失败"
        exit 1
    fi
}

# 函数：备份当前部署
backup_current() {
    if [ "$(docker ps -q -f name=$PROJECT_NAME)" ]; then
        print_message $BLUE "💾 备份当前部署..."
        
        local backup_name="backup-$(date +%Y%m%d-%H%M%S)"
        local backup_path="$BACKUP_DIR/$backup_name"
        
        mkdir -p $backup_path
        
        # 备份网站文件
        cp -r index.html styles scripts $backup_path/
        
        # 备份配置文件
        cp -r nginx $backup_path/
        
        # 备份Docker配置
        cp $COMPOSE_FILE $backup_path/
        
        print_message $GREEN "✅ 备份完成: $backup_path"
    fi
}

# 函数：部署应用
deploy() {
    print_message $BLUE "🚀 开始部署 $PROJECT_NAME..."
    
    # 停止现有容器
    if [ "$(docker ps -q -f name=$PROJECT_NAME)" ]; then
        print_message $YELLOW "⏹️  停止现有容器..."
        docker-compose down
    fi
    
    # 拉取最新镜像
    print_message $BLUE "📥 拉取最新Nginx镜像..."
    docker-compose pull
    
    # 启动服务
    print_message $BLUE "🔄 启动服务..."
    docker-compose up -d
    
    # 等待服务启动
    print_message $BLUE "⏳ 等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if [ "$(docker ps -q -f name=$PROJECT_NAME)" ]; then
        print_message $GREEN "✅ 服务启动成功"
    else
        print_message $RED "❌ 服务启动失败"
        docker-compose logs
        exit 1
    fi
}

# 函数：健康检查
health_check() {
    print_message $BLUE "🏥 执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost/health &> /dev/null; then
            print_message $GREEN "✅ 健康检查通过"
            return 0
        fi
        
        print_message $YELLOW "⏳ 健康检查失败，重试 $attempt/$max_attempts..."
        sleep 2
        ((attempt++))
    done
    
    print_message $RED "❌ 健康检查失败"
    docker-compose logs
    return 1
}

# 函数：显示部署信息
show_info() {
    print_message $GREEN "🎉 部署完成！"
    echo
    print_message $BLUE "📊 部署信息:"
    echo "  项目名称: $PROJECT_NAME"
    echo "  访问地址: http://localhost"
    echo "  健康检查: http://localhost/health"
    echo "  配置模式: 统一生产就绪配置"
    echo
    print_message $BLUE "🔧 管理命令:"
    echo "  查看日志: docker-compose logs -f"
    echo "  重启服务: docker-compose restart"
    echo "  停止服务: docker-compose down"
    echo "  查看状态: docker-compose ps"
    echo
    print_message $BLUE "📁 重要目录:"
    echo "  网站文件: $(pwd)"
    echo "  Nginx配置: $(pwd)/nginx"
    echo "  日志文件: $(pwd)/logs/nginx"
    echo "  备份文件: $(pwd)/backups"
    echo
    print_message $BLUE "⚙️ 环境配置:"
    echo "  环境变量文件: .env (如果存在)"
    echo "  开发环境: 复制 .env.example 到 .env 并调整配置"
    echo "  生产环境: 使用默认配置或设置环境变量"
}

# 函数：清理旧备份
cleanup_backups() {
    print_message $BLUE "🧹 清理旧备份..."
    
    # 保留最近10个备份
    if [ -d "$BACKUP_DIR" ]; then
        cd $BACKUP_DIR
        ls -t | tail -n +11 | xargs -r rm -rf
        cd - > /dev/null
        print_message $GREEN "✅ 备份清理完成"
    fi
}

# 主函数
main() {
    print_message $GREEN "🚀 安徽追逐科技有限公司官网 - Docker部署脚本"
    print_message $GREEN "=================================================="
    
    check_requirements
    create_directories
    validate_config
    backup_current
    deploy
    
    if health_check; then
        cleanup_backups
        show_info
    else
        print_message $RED "❌ 部署失败，请检查日志"
        exit 1
    fi
}

# 脚本参数处理
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "stop")
        print_message $BLUE "⏹️  停止服务..."
        docker-compose down
        print_message $GREEN "✅ 服务已停止"
        ;;
    "restart")
        print_message $BLUE "🔄 重启服务..."
        docker-compose restart
        print_message $GREEN "✅ 服务已重启"
        ;;
    "logs")
        docker-compose logs -f
        ;;
    "status")
        docker-compose ps
        ;;
    "update")
        print_message $BLUE "🔄 更新服务..."
        docker-compose pull
        docker-compose up -d
        health_check && print_message $GREEN "✅ 更新完成"
        ;;
    "backup")
        backup_current
        ;;
    "help")
        echo "用法: $0 [命令]"
        echo "命令:"
        echo "  deploy  - 部署服务（默认）"
        echo "  stop    - 停止服务"
        echo "  restart - 重启服务"
        echo "  logs    - 查看日志"
        echo "  status  - 查看状态"
        echo "  update  - 更新服务"
        echo "  backup  - 手动备份"
        echo "  help    - 显示帮助"
        ;;
    *)
        print_message $RED "❌ 未知命令: $1"
        print_message $BLUE "使用 '$0 help' 查看帮助"
        exit 1
        ;;
esac

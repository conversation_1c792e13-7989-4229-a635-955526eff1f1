version: '3.8'

services:
  # 安徽追逐科技官网 - Nginx Web服务器 (生产就绪统一配置)
  yaosen-website:
    image: nginx:1.25-alpine
    container_name: yaosen-website
    restart: unless-stopped

    # 端口映射
    ports:
      - "80:80"
      - "443:443"

    # 目录映射 (安全优化 - 只映射必要文件)
    volumes:
      # 网站文件映射 (精确映射，避免暴露敏感文件)
      - ./index.html:/usr/share/nginx/html/index.html:ro
      - ./styles:/usr/share/nginx/html/styles:ro
      - ./scripts:/usr/share/nginx/html/scripts:ro
      - ./favicon.ico:/usr/share/nginx/html/favicon.ico:ro
      # Nginx配置映射
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      # SSL证书映射
      - ./ssl:/etc/nginx/ssl:ro
      # 日志映射
      - ./logs/nginx:/var/log/nginx

    # 环境变量
    environment:
      - TZ=Asia/Shanghai
      - NGINX_ENVSUBST_TEMPLATE_DIR=/etc/nginx/templates
      - NGINX_ENVSUBST_OUTPUT_DIR=/etc/nginx/conf.d
      # 可通过环境变量控制的配置
      - ENVIRONMENT=${ENVIRONMENT:-production}

    # 资源限制 (生产就绪配置)
    deploy:
      resources:
        limits:
          memory: ${MEMORY_LIMIT:-512M}
          cpus: ${CPU_LIMIT:-1.0}
        reservations:
          memory: ${MEMORY_RESERVATION:-256M}
          cpus: ${CPU_RESERVATION:-0.5}
      restart_policy:
        condition: any
        delay: 5s
        max_attempts: 3
        window: 120s

    # 健康检查 (可配置间隔)
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: ${HEALTH_INTERVAL:-30s}
      timeout: ${HEALTH_TIMEOUT:-10s}
      retries: ${HEALTH_RETRIES:-3}
      start_period: ${HEALTH_START_PERIOD:-40s}

    # 生产环境日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
        compress: "true"

    # 安全配置
    security_opt:
      - no-new-privileges:true
    user: "101:101"  # nginx用户

    # 只读根文件系统 (可通过环境变量控制)
    read_only: ${READ_ONLY_FS:-true}

    # 临时文件系统
    tmpfs:
      - /var/cache/nginx:rw,noexec,nosuid,size=100m
      - /var/run:rw,noexec,nosuid,size=100m
      - /tmp:rw,noexec,nosuid,size=100m

    # 网络配置
    networks:
      - yaosen-network

    # 标签 (包含生产环境标签)
    labels:
      - "com.yaosen.service=website"
      - "com.yaosen.environment=${ENVIRONMENT:-production}"
      - "com.yaosen.version=1.0.0"
      - "com.yaosen.maintainer=<EMAIL>"
      # Traefik标签 (如果使用Traefik)
      - "traefik.enable=${TRAEFIK_ENABLE:-false}"
      - "traefik.http.routers.yaosen.rule=Host(`${DOMAIN_NAME:-localhost}`)"
      - "traefik.http.routers.yaosen.tls=${TLS_ENABLE:-false}"

# 网络配置
networks:
  yaosen-network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: yaosen-br
    ipam:
      config:
        - subnet: **********/16
          gateway: **********

# 数据卷配置
volumes:
  nginx-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./logs/nginx

version: '3.8'

# 生产环境配置覆盖
services:
  yaosen-website:
    # 生产环境镜像（可以固定版本）
    image: nginx:1.25-alpine
    
    # 生产环境重启策略
    restart: always
    
    # 生产环境资源限制（更严格）
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'
      restart_policy:
        condition: any
        delay: 5s
        max_attempts: 3
        window: 120s
    
    # 生产环境健康检查（更频繁）
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 15s
      timeout: 5s
      retries: 5
      start_period: 30s
    
    # 生产环境日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
        compress: "true"
    
    # 生产环境安全配置
    security_opt:
      - no-new-privileges:true
    
    # 只读根文件系统（增强安全性）
    read_only: true
    
    # 临时文件系统
    tmpfs:
      - /var/cache/nginx:rw,noexec,nosuid,size=100m
      - /var/run:rw,noexec,nosuid,size=100m
      - /tmp:rw,noexec,nosuid,size=100m
    
    # 生产环境标签
    labels:
      - "com.yaosen.service=website"
      - "com.yaosen.environment=production"
      - "com.yaosen.version=1.0.0"
      - "com.yaosen.maintainer=<EMAIL>"
      - "traefik.enable=true"
      - "traefik.http.routers.yaosen.rule=Host(`your-domain.com`)"
      - "traefik.http.routers.yaosen.tls=true"
      - "traefik.http.routers.yaosen.tls.certresolver=letsencrypt"

# 生产环境网络配置
networks:
  yaosen-network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: yaosen-br
    ipam:
      config:
        - subnet: **********/16
          gateway: **********

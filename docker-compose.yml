version: '3.8'

services:
  # 安徽追逐科技官网 - Nginx Web服务器
  yaosen-website:
    image: nginx:1.25-alpine
    container_name: yaosen-website
    restart: unless-stopped
    
    # 端口映射
    ports:
      - "80:80"
      - "443:443"
    
    # 目录映射
    volumes:
      # 网站文件映射
      - ./:/usr/share/nginx/html:ro
      # Nginx配置映射
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      # SSL证书映射（如果有）
      - ./ssl:/etc/nginx/ssl:ro
      # 日志映射
      - ./logs/nginx:/var/log/nginx
    
    # 环境变量
    environment:
      - TZ=Asia/Shanghai
      - NGINX_ENVSUBST_TEMPLATE_DIR=/etc/nginx/templates
      - NGINX_ENVSUBST_OUTPUT_DIR=/etc/nginx/conf.d
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 网络配置
    networks:
      - yaosen-network
    
    # 标签
    labels:
      - "com.yaosen.service=website"
      - "com.yaosen.environment=production"
      - "com.yaosen.version=1.0.0"

# 网络配置
networks:
  yaosen-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置（可选）
volumes:
  nginx-logs:
    driver: local

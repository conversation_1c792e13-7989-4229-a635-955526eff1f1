#!/bin/bash

# 安徽追逐科技有限公司官网 - 监控和维护脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
PROJECT_NAME="yaosen-website"
LOG_DIR="./logs/nginx"
BACKUP_DIR="./backups"
ALERT_EMAIL="<EMAIL>"

# 函数：打印消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] ${message}${NC}"
}

# 函数：检查容器状态
check_container_status() {
    print_message $BLUE "🔍 检查容器状态..."
    
    if [ "$(docker ps -q -f name=$PROJECT_NAME)" ]; then
        local status=$(docker inspect --format='{{.State.Status}}' $PROJECT_NAME)
        local health=$(docker inspect --format='{{.State.Health.Status}}' $PROJECT_NAME 2>/dev/null || echo "unknown")
        
        print_message $GREEN "✅ 容器状态: $status"
        print_message $GREEN "✅ 健康状态: $health"
        
        if [ "$status" != "running" ] || [ "$health" = "unhealthy" ]; then
            print_message $RED "❌ 容器状态异常"
            return 1
        fi
    else
        print_message $RED "❌ 容器未运行"
        return 1
    fi
}

# 函数：检查网站可访问性
check_website_accessibility() {
    print_message $BLUE "🌐 检查网站可访问性..."
    
    local response_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/ || echo "000")
    local response_time=$(curl -s -o /dev/null -w "%{time_total}" http://localhost/ || echo "0")
    
    if [ "$response_code" = "200" ]; then
        print_message $GREEN "✅ 网站可访问 (HTTP $response_code, ${response_time}s)"
    else
        print_message $RED "❌ 网站不可访问 (HTTP $response_code)"
        return 1
    fi
    
    # 检查健康检查端点
    local health_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/health || echo "000")
    if [ "$health_code" = "200" ]; then
        print_message $GREEN "✅ 健康检查端点正常"
    else
        print_message $RED "❌ 健康检查端点异常 (HTTP $health_code)"
        return 1
    fi
}

# 函数：检查资源使用情况
check_resource_usage() {
    print_message $BLUE "📊 检查资源使用情况..."
    
    if [ "$(docker ps -q -f name=$PROJECT_NAME)" ]; then
        local stats=$(docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" $PROJECT_NAME)
        echo "$stats"
        
        # 获取内存使用百分比
        local mem_percent=$(docker stats --no-stream --format "{{.MemPerc}}" $PROJECT_NAME | sed 's/%//')
        local cpu_percent=$(docker stats --no-stream --format "{{.CPUPerc}}" $PROJECT_NAME | sed 's/%//')
        
        # 检查资源使用阈值
        if (( $(echo "$mem_percent > 80" | bc -l) )); then
            print_message $YELLOW "⚠️  内存使用率过高: ${mem_percent}%"
        fi
        
        if (( $(echo "$cpu_percent > 80" | bc -l) )); then
            print_message $YELLOW "⚠️  CPU使用率过高: ${cpu_percent}%"
        fi
    fi
}

# 函数：检查日志错误
check_logs() {
    print_message $BLUE "📋 检查最近的错误日志..."
    
    if [ -f "$LOG_DIR/error.log" ]; then
        local error_count=$(tail -n 1000 "$LOG_DIR/error.log" | grep -c "error\|crit\|alert\|emerg" || echo "0")
        
        if [ "$error_count" -gt 0 ]; then
            print_message $YELLOW "⚠️  发现 $error_count 个错误日志条目"
            echo "最近的错误:"
            tail -n 1000 "$LOG_DIR/error.log" | grep "error\|crit\|alert\|emerg" | tail -n 5
        else
            print_message $GREEN "✅ 无错误日志"
        fi
    fi
}

# 函数：检查磁盘空间
check_disk_space() {
    print_message $BLUE "💾 检查磁盘空间..."
    
    local disk_usage=$(df -h . | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$disk_usage" -gt 80 ]; then
        print_message $RED "❌ 磁盘空间不足: ${disk_usage}%"
        return 1
    elif [ "$disk_usage" -gt 70 ]; then
        print_message $YELLOW "⚠️  磁盘空间警告: ${disk_usage}%"
    else
        print_message $GREEN "✅ 磁盘空间充足: ${disk_usage}%"
    fi
}

# 函数：清理日志
cleanup_logs() {
    print_message $BLUE "🧹 清理旧日志..."
    
    # 清理超过30天的日志
    find $LOG_DIR -name "*.log" -type f -mtime +30 -delete 2>/dev/null || true
    
    # 压缩超过7天的日志
    find $LOG_DIR -name "*.log" -type f -mtime +7 ! -name "*.gz" -exec gzip {} \; 2>/dev/null || true
    
    print_message $GREEN "✅ 日志清理完成"
}

# 函数：生成报告
generate_report() {
    local report_file="./reports/monitor-report-$(date +%Y%m%d-%H%M%S).txt"
    mkdir -p ./reports
    
    {
        echo "安徽追逐科技有限公司官网 - 监控报告"
        echo "生成时间: $(date)"
        echo "=================================="
        echo
        
        echo "容器状态:"
        docker ps -f name=$PROJECT_NAME --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        echo
        
        echo "资源使用:"
        docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" $PROJECT_NAME
        echo
        
        echo "磁盘使用:"
        df -h .
        echo
        
        echo "最近访问统计:"
        if [ -f "$LOG_DIR/access.log" ]; then
            tail -n 1000 "$LOG_DIR/access.log" | awk '{print $1}' | sort | uniq -c | sort -nr | head -10
        fi
        
    } > $report_file
    
    print_message $GREEN "✅ 报告已生成: $report_file"
}

# 函数：发送告警
send_alert() {
    local message="$1"
    
    # 这里可以集成邮件、短信、钉钉等告警方式
    print_message $RED "🚨 告警: $message"
    
    # 示例：发送邮件告警（需要配置邮件服务）
    # echo "$message" | mail -s "耀森官网告警" $ALERT_EMAIL
    
    # 示例：写入告警日志
    echo "[$(date)] ALERT: $message" >> ./alerts.log
}

# 函数：自动修复
auto_repair() {
    print_message $BLUE "🔧 尝试自动修复..."
    
    # 如果容器未运行，尝试重启
    if ! check_container_status &>/dev/null; then
        print_message $YELLOW "⚠️  容器异常，尝试重启..."
        docker-compose restart
        sleep 10
        
        if check_container_status &>/dev/null; then
            print_message $GREEN "✅ 自动修复成功"
            send_alert "容器已自动重启并恢复正常"
        else
            print_message $RED "❌ 自动修复失败"
            send_alert "容器自动重启失败，需要人工干预"
            return 1
        fi
    fi
}

# 主监控函数
main_monitor() {
    print_message $GREEN "🔍 开始监控检查..."
    
    local issues=0
    
    # 执行各项检查
    check_container_status || ((issues++))
    check_website_accessibility || ((issues++))
    check_resource_usage
    check_logs
    check_disk_space || ((issues++))
    
    # 如果发现问题，尝试自动修复
    if [ $issues -gt 0 ]; then
        print_message $YELLOW "⚠️  发现 $issues 个问题"
        auto_repair
    else
        print_message $GREEN "✅ 所有检查通过"
    fi
    
    # 定期清理
    cleanup_logs
}

# 脚本参数处理
case "${1:-monitor}" in
    "monitor")
        main_monitor
        ;;
    "report")
        generate_report
        ;;
    "cleanup")
        cleanup_logs
        ;;
    "status")
        check_container_status
        check_website_accessibility
        check_resource_usage
        ;;
    "repair")
        auto_repair
        ;;
    "help")
        echo "用法: $0 [命令]"
        echo "命令:"
        echo "  monitor - 执行完整监控检查（默认）"
        echo "  report  - 生成监控报告"
        echo "  cleanup - 清理日志文件"
        echo "  status  - 检查当前状态"
        echo "  repair  - 尝试自动修复"
        echo "  help    - 显示帮助"
        ;;
    *)
        print_message $RED "❌ 未知命令: $1"
        exit 1
        ;;
esac

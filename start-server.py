#!/usr/bin/env python3
"""
安徽追逐科技有限公司官网 - 本地测试服务器
简单的HTTP服务器，用于本地测试静态网站
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

# 配置
PORT = 8000
HOST = 'localhost'

def main():
    # 确保在正确的目录中运行
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 检查必要文件是否存在
    required_files = ['index.html', 'styles/main.css', 'scripts/main.js']
    missing_files = []
    
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ 错误: 以下必要文件缺失:")
        for file in missing_files:
            print(f"   - {file}")
        sys.exit(1)
    
    # 创建HTTP服务器
    handler = http.server.SimpleHTTPRequestHandler
    
    try:
        with socketserver.TCPServer((HOST, PORT), handler) as httpd:
            url = f"http://{HOST}:{PORT}"
            
            print("🚀 安徽追逐科技有限公司官网 - 本地测试服务器")
            print("=" * 50)
            print(f"📍 服务器地址: {url}")
            print(f"📁 根目录: {script_dir}")
            print("=" * 50)
            print("✅ 服务器启动成功!")
            print("🌐 正在打开浏览器...")
            print("\n按 Ctrl+C 停止服务器")
            
            # 自动打开浏览器
            webbrowser.open(url)
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n🛑 服务器已停止")
        print("👋 感谢使用!")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 错误: 端口 {PORT} 已被占用")
            print(f"💡 请尝试使用其他端口或关闭占用该端口的程序")
        else:
            print(f"❌ 错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

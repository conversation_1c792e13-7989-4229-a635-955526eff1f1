/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 基础样式 */
:root {
    --primary-50: #eff6ff;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-900: #1e3a8a;
    
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --border-radius: 8px;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: #ffffff;
}

/* 容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* 导航栏 */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--gray-200);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-icon {
    color: white;
    font-weight: 700;
    font-size: 1.125rem;
}

.brand-name {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

.brand-subtitle {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin: 0;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: var(--gray-600);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-600);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-600);
    border-radius: 1px;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 0.25rem;
}

.nav-toggle span {
    width: 1.5rem;
    height: 2px;
    background: var(--gray-600);
    transition: all 0.3s ease;
}

/* 主要内容 */
main {
    margin-top: 80px;
}

/* 首页横幅 */
.hero {
    position: relative;
    min-height: 90vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--gray-50) 100%);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="%23e5e7eb" stroke-width="1" opacity="0.3"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
}

.hero .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: var(--gray-900);
}

.highlight {
    color: var(--primary-600);
    position: relative;
}

.highlight::after {
    content: '';
    position: absolute;
    bottom: 0.25rem;
    left: 0;
    right: 0;
    height: 0.5rem;
    background: linear-gradient(90deg, var(--primary-200), var(--primary-300));
    border-radius: 0.25rem;
    z-index: -1;
}

.hero-description {
    font-size: 1.125rem;
    color: var(--gray-600);
    margin-bottom: 2rem;
    line-height: 1.7;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: var(--primary-600);
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    background: var(--primary-700);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: white;
    color: var(--primary-600);
    border: 2px solid var(--primary-600);
}

.btn-secondary:hover {
    background: var(--primary-600);
    color: white;
    transform: translateY(-2px);
}

.hero-image {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

.hero-card {
    background: white;
    padding: 1.5rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-lg);
    text-align: center;
    transition: transform 0.3s ease;
}

.hero-card:hover {
    transform: translateY(-5px);
}

.card-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.hero-card h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--gray-900);
}

.hero-card p {
    color: var(--gray-600);
    font-size: 0.875rem;
}

/* 通用区块样式 */
section {
    padding: 5rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

/* 关于我们 */
.about {
    background: var(--gray-50);
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: start;
}

.about-text h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.about-text p {
    color: var(--gray-600);
    margin-bottom: 2rem;
    line-height: 1.7;
}

.advantage-list {
    list-style: none;
}

.advantage-list li {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    align-items: flex-start;
}

.advantage-icon {
    width: 1.5rem;
    height: 1.5rem;
    background: var(--primary-600);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 700;
    flex-shrink: 0;
    margin-top: 0.125rem;
}

.advantage-list strong {
    color: var(--gray-900);
    display: block;
    margin-bottom: 0.25rem;
}

.advantage-list p {
    color: var(--gray-600);
    margin: 0;
    font-size: 0.875rem;
}

.about-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.stat-item {
    text-align: center;
    padding: 2rem 1rem;
    background: white;
    border-radius: 1rem;
    box-shadow: var(--shadow);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-600);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--gray-600);
    font-weight: 500;
}

/* 服务项目 */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.service-card {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border: 1px solid var(--gray-200);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-300);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.service-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.service-description {
    color: var(--gray-600);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-features {
    list-style: none;
}

.service-features li {
    color: var(--gray-600);
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1.5rem;
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-600);
    font-weight: 700;
}

/* 成功案例 */
.cases {
    background: var(--gray-50);
}

.cases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.case-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.case-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.case-image {
    height: 200px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.case-category {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.case-content {
    padding: 1.5rem;
}

.case-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.case-description {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.case-results {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.result-item {
    background: var(--primary-50);
    color: var(--primary-700);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
}

/* 联系我们 */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info h3,
.contact-form h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.contact-icon {
    font-size: 1.25rem;
    margin-top: 0.125rem;
}

.contact-item strong {
    color: var(--gray-900);
    display: block;
    margin-bottom: 0.5rem;
}

.contact-item p {
    color: var(--gray-600);
    margin: 0;
    line-height: 1.5;
}

/* 表单样式 */
.form {
    background: var(--gray-50);
    padding: 2rem;
    border-radius: 1rem;
    border: 1px solid var(--gray-200);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form button {
    width: 100%;
}

/* 页脚样式 */
.footer {
    background: var(--gray-900);
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.footer-section h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: white;
}

.footer-section p,
.footer-section li {
    color: var(--gray-400);
    font-size: 0.875rem;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.75rem;
}

.footer-section a {
    color: var(--gray-400);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: white;
}

.company-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.company-logo {
    width: 2.5rem;
    height: 2.5rem;
    background: var(--primary-600);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.125rem;
}

.company-details h3 {
    font-size: 1.125rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.company-details p {
    font-size: 0.875rem;
    color: var(--gray-400);
    margin: 0;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.contact-icon {
    width: 1.125rem;
    height: 1.125rem;
    color: var(--primary-400);
    margin-top: 0.125rem;
    flex-shrink: 0;
}

/* 版权信息 */
.copyright {
    border-top: 1px solid var(--gray-800);
    padding-top: 2rem;
}

.copyright-content {
    background: rgba(17, 24, 39, 0.5);
    backdrop-filter: blur(10px);
    border-radius: 0.75rem;
    padding: 1.5rem 2rem;
    border: 1px solid rgba(75, 85, 99, 0.3);
}

.copyright-main {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    font-size: 0.875rem;
    color: var(--gray-400);
}

.copyright-left {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
}

.copyright-right {
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.3));
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid rgba(59, 130, 246, 0.4);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(4px);
    order: -1;
}

.copyright-highlight {
    font-weight: 600;
    color: var(--gray-100);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.pulse-dot {
    width: 0.5rem;
    height: 0.5rem;
    background: var(--primary-400);
    border-radius: 50%;
    animation: pulse 2s infinite;
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
}

.pulse-dot-purple {
    background: #a855f7;
    box-shadow: 0 0 0 0 rgba(168, 85, 247, 0.7);
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }
    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
}

.copyright-item {
    transition: color 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.copyright-item:hover {
    color: var(--primary-400);
}

.copyright-dot {
    width: 0.25rem;
    height: 0.25rem;
    background: var(--gray-500);
    border-radius: 50%;
}

.copyright-decorative {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    margin: 1.5rem 0;
}

.decorative-line {
    width: 4rem;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
}

.decorative-dots {
    display: flex;
    gap: 0.25rem;
}

.decorative-dot {
    width: 0.375rem;
    height: 0.375rem;
    border-radius: 50%;
    animation: bounce 2s infinite;
}

.decorative-dot:nth-child(1) {
    background: var(--primary-500);
    animation-delay: 0s;
}

.decorative-dot:nth-child(2) {
    background: var(--primary-400);
    animation-delay: 0.2s;
}

.decorative-dot:nth-child(3) {
    background: var(--primary-300);
    animation-delay: 0.4s;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.copyright-footer {
    text-align: center;
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-top: 1rem;
}

.policy-links {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 1rem;
}

.policy-links a {
    color: var(--gray-400);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.policy-links a:hover {
    color: white;
}

/* 响应式设计 */
@media (min-width: 768px) {
    .copyright-main {
        flex-direction: row;
        gap: 2rem;
    }

    .copyright-left {
        flex-direction: row;
        gap: 1.5rem;
    }

    .copyright-right {
        order: 0;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        flex-direction: column;
        padding: 1rem;
        box-shadow: var(--shadow-lg);
        border-top: 1px solid var(--gray-200);
    }

    .nav-menu.active {
        display: flex;
    }

    .nav-toggle {
        display: flex;
    }

    .hero .container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-stats {
        grid-template-columns: 1fr 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 0.75rem;
    }

    .hero-title {
        font-size: 1.75rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .about-stats {
        grid-template-columns: 1fr;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
    }
}

#!/bin/bash

# Docker配置测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_message $BLUE "🔧 Docker配置修复测试"
print_message $BLUE "========================"

# 1. 检查必要文件
print_message $BLUE "📁 检查必要文件..."
required_files=(
    "docker-compose.yml"
    "nginx/nginx.conf"
    "nginx/conf.d/yaosen-website.conf"
    "index.html"
    "styles/main.css"
    "scripts/main.js"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_message $GREEN "✅ $file 存在"
    else
        print_message $RED "❌ $file 缺失"
        exit 1
    fi
done

# 2. 创建必要目录
print_message $BLUE "📁 创建必要目录..."
mkdir -p logs/nginx
mkdir -p ssl
print_message $GREEN "✅ 目录创建完成"

# 3. 检查Docker配置语法
print_message $BLUE "🔧 检查Docker配置语法..."
if command -v docker-compose &> /dev/null; then
    if docker-compose config > /dev/null 2>&1; then
        print_message $GREEN "✅ Docker配置语法正确"
    else
        print_message $RED "❌ Docker配置语法错误"
        docker-compose config
        exit 1
    fi
else
    print_message $YELLOW "⚠️  Docker Compose未安装，跳过语法检查"
fi

# 4. 检查Nginx配置语法
print_message $BLUE "🔧 检查Nginx配置语法..."
if command -v docker &> /dev/null; then
    if docker run --rm -v "$(pwd)/nginx/nginx.conf:/etc/nginx/nginx.conf:ro" \
        -v "$(pwd)/nginx/conf.d:/etc/nginx/conf.d:ro" \
        nginx:1.25-alpine nginx -t > /dev/null 2>&1; then
        print_message $GREEN "✅ Nginx配置语法正确"
    else
        print_message $RED "❌ Nginx配置语法错误"
        docker run --rm -v "$(pwd)/nginx/nginx.conf:/etc/nginx/nginx.conf:ro" \
            -v "$(pwd)/nginx/conf.d:/etc/nginx/conf.d:ro" \
            nginx:1.25-alpine nginx -t
        exit 1
    fi
else
    print_message $YELLOW "⚠️  Docker未安装，跳过Nginx配置检查"
fi

# 5. 显示配置摘要
print_message $BLUE "📊 配置摘要..."
echo "  项目名称: yaosen-website"
echo "  Nginx版本: 1.25-alpine"
echo "  端口映射: 80:80, 443:443"
echo "  只读文件系统: 默认关闭"
echo "  安全配置: 启用"
echo "  日志目录: ./logs/nginx"

print_message $GREEN "🎉 配置检查完成！"
print_message $BLUE "💡 下一步操作:"
echo "  1. 运行: docker-compose up -d"
echo "  2. 测试: curl http://localhost/"
echo "  3. 健康检查: curl http://localhost/health"
echo "  4. 查看日志: docker-compose logs -f"

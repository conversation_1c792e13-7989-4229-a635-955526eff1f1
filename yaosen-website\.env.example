# 环境变量配置示例
# 复制此文件为 .env.local 并根据需要修改

# 应用配置
NODE_ENV=development
PORT=3000
NEXT_PUBLIC_APP_NAME="耀森企业管理系统"
NEXT_PUBLIC_APP_VERSION="1.0.0"

# 网站配置
NEXT_PUBLIC_SITE_URL="http://localhost:3000"
NEXT_PUBLIC_SITE_NAME="安徽追逐科技有限公司"
NEXT_PUBLIC_API_BASE_URL="/api"

# 公司信息
NEXT_PUBLIC_COMPANY_PHONE="************"
NEXT_PUBLIC_COMPANY_EMAIL="<EMAIL>"
NEXT_PUBLIC_COMPANY_ADDRESS="安徽省合肥市蜀山区蜀山经济开发区中国(安徽)自由贸易试验区合肥片区花峰路1201号跨境电商产业园三期3幢G区8层1326号"
NEXT_PUBLIC_ICP_NUMBER="皖ICP备2025084445号-1"

# 地图配置
NEXT_PUBLIC_BAIDU_MAP_KEY="your_baidu_map_api_key"
NEXT_PUBLIC_AMAP_KEY="your_amap_api_key"

# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/yaosen_dev"
REDIS_URL="redis://localhost:6379"

# 认证配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"
JWT_SECRET="your_jwt_secret_key"

# 邮件服务配置
SMTP_HOST="smtp.example.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your_email_password"
SMTP_FROM="<EMAIL>"

# 文件上传配置
UPLOAD_MAX_SIZE="10485760"  # 10MB
ALLOWED_FILE_TYPES="jpg,jpeg,png,pdf,doc,docx,xls,xlsx"
UPLOAD_PATH="/uploads"

# 安全配置
ENCRYPTION_KEY="your_encryption_key"
CORS_ORIGIN="http://localhost:3000"
RATE_LIMIT_MAX="100"
RATE_LIMIT_WINDOW="900000"  # 15分钟

# 监控和日志配置
LOG_LEVEL="info"
SENTRY_DSN="your_sentry_dsn"
SENTRY_ENVIRONMENT="development"

# 分析服务
ANALYTICS_ID="your_google_analytics_id"
BAIDU_ANALYTICS_ID="your_baidu_analytics_id"

# 功能开关
FEATURE_USER_REGISTRATION="true"
FEATURE_EMAIL_VERIFICATION="true"
FEATURE_SMS_VERIFICATION="false"

# 开发工具配置
ANALYZE="false"
DEBUG="false"
VERBOSE_LOGGING="false"

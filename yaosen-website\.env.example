# 环境变量配置示例
# 复制此文件为 .env.local 并根据需要修改

# 应用配置
NODE_ENV=production
PORT=3000

# 网站配置
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_SITE_NAME=漯河耀森企业管理有限公司

# 联系信息
NEXT_PUBLIC_COMPANY_PHONE=************
NEXT_PUBLIC_COMPANY_EMAIL=<EMAIL>
NEXT_PUBLIC_COMPANY_ADDRESS=河南省漯河市临颍县固厢乡中心社区门面房31号

# 地图配置
NEXT_PUBLIC_BAIDU_MAP_KEY=your_baidu_map_api_key
NEXT_PUBLIC_AMAP_KEY=your_amap_api_key

# 邮件配置（如果需要）
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# 数据库配置（如果需要）
DATABASE_URL=postgresql://username:password@localhost:5432/yaosen_db

# 安全配置
JWT_SECRET=your_jwt_secret_key
ENCRYPTION_KEY=your_encryption_key

# 第三方服务（如果需要）
ANALYTICS_ID=your_google_analytics_id
SENTRY_DSN=your_sentry_dsn

{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/vscode/www1/yaosen-website/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { Menu, X, Phone, Mail } from 'lucide-react';\n\nconst Navigation = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const navItems = [\n    { href: '/', label: '首页' },\n    { href: '/about', label: '关于我们' },\n    { href: '/services', label: '服务项目' },\n    { href: '/cases', label: '成功案例' },\n    { href: '/contact', label: '联系我们' },\n  ];\n\n  return (\n    <nav className=\"bg-white shadow-lg sticky top-0 z-50\">\n      {/* 顶部联系信息栏 */}\n      <div className=\"bg-blue-900 text-white py-2\">\n        <div className=\"container mx-auto px-4 flex justify-between items-center text-sm\">\n          <div className=\"flex items-center space-x-6\">\n            <div className=\"flex items-center space-x-2\">\n              <Phone size={14} />\n              <span>************</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Mail size={14} />\n              <span><EMAIL></span>\n            </div>\n          </div>\n          <div className=\"hidden md:block\">\n            <span>综合企业服务专家 · 值得信赖的合作伙伴</span>\n          </div>\n        </div>\n      </div>\n\n      {/* 主导航栏 */}\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-3\">\n            <div className=\"w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-xl\">追</span>\n            </div>\n            <div>\n              <h1 className=\"text-xl font-bold text-gray-800\">追逐科技</h1>\n              <p className=\"text-sm text-gray-600\">安徽追逐科技有限公司</p>\n            </div>\n          </Link>\n\n          {/* 桌面端导航菜单 */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200 relative group\"\n              >\n                {item.label}\n                <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"></span>\n              </Link>\n            ))}\n          </div>\n\n          {/* 移动端菜单按钮 */}\n          <button\n            className=\"md:hidden p-2 rounded-lg hover:bg-gray-100\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n          </button>\n        </div>\n\n        {/* 移动端导航菜单 */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"block py-3 px-4 text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {item.label}\n              </Link>\n            ))}\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,aAAa;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAK;QACzB;YAAE,MAAM;YAAU,OAAO;QAAO;QAChC;YAAE,MAAM;YAAa,OAAO;QAAO;QACnC;YAAE,MAAM;YAAU,OAAO;QAAO;QAChC;YAAE,MAAM;YAAY,OAAO;QAAO;KACnC;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,MAAM;;;;;;sDACb,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;sDACZ,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAGV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAChD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAKzC,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;4CAET,KAAK,KAAK;0DACX,8OAAC;gDAAK,WAAU;;;;;;;uCALX,KAAK,IAAI;;;;;;;;;;0CAWpB,8OAAC;gCACC,WAAU;gCACV,SAAS,IAAM,cAAc,CAAC;0CAE7B,2BAAa,8OAAC,4LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;yDAAS,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;;;;;;;;;;;;oBAK/C,4BACC,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,KAAK,KAAK;+BALN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;AAa9B;uCAEe", "debugId": null}}]}
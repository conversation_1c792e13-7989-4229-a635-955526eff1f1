{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/vscode/www1/yaosen-website/src/app/contact/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Phone, Mail, MapPin, Clock, Send, ExternalLink } from 'lucide-react';\n\nexport default function Contact() {\n  const [formData, setFormData] = useState({\n    name: '',\n    company: '',\n    phone: '',\n    email: '',\n    service: '',\n    message: ''\n  });\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // 这里可以添加表单提交逻辑\n    console.log('表单数据:', formData);\n    alert('感谢您的咨询，我们会尽快与您联系！');\n  };\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <h1 className=\"text-4xl lg:text-5xl font-bold mb-6\">联系我们</h1>\n            <p className=\"text-xl lg:text-2xl text-blue-100\">\n              专业的综合服务团队，随时为您提供全方位的咨询和支持\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* 联系信息和表单 */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n            {/* 联系信息 */}\n            <div>\n              <h2 className=\"text-3xl font-bold text-gray-800 mb-8\">联系信息</h2>\n              \n              <div className=\"space-y-6\">\n                <div className=\"flex items-start space-x-4\">\n                  <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n                    <MapPin className=\"text-blue-600\" size={24} />\n                  </div>\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-gray-800 mb-1\">公司地址</h3>\n                    <p className=\"text-gray-600\">安徽省合肥市蜀山区蜀山经济开发区</p>\n                    <p className=\"text-gray-600\">中国(安徽)自由贸易试验区合肥片区</p>\n                    <p className=\"text-gray-600\">花峰路1201号跨境电商产业园三期3幢G区8层1326号</p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-start space-x-4\">\n                  <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n                    <Phone className=\"text-green-600\" size={24} />\n                  </div>\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-gray-800 mb-1\">联系电话</h3>\n                    <p className=\"text-gray-600\">************</p>\n                    <p className=\"text-gray-600\">0395-123-4567</p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-start space-x-4\">\n                  <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n                    <Mail className=\"text-purple-600\" size={24} />\n                  </div>\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-gray-800 mb-1\">电子邮箱</h3>\n                    <p className=\"text-gray-600\"><EMAIL></p>\n                    <p className=\"text-gray-600\"><EMAIL></p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-start space-x-4\">\n                  <div className=\"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center\">\n                    <Clock className=\"text-orange-600\" size={24} />\n                  </div>\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-gray-800 mb-1\">工作时间</h3>\n                    <p className=\"text-gray-600\">周一至周五：9:00 - 18:00</p>\n                    <p className=\"text-gray-600\">周六：9:00 - 17:00</p>\n                  </div>\n                </div>\n              </div>\n              \n              {/* 位置地图 */}\n              <div className=\"mt-8\">\n                <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">位置地图</h3>\n                <div className=\"w-full h-64 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg overflow-hidden relative\">\n                  {/* 地图位置显示 */}\n                  <div className=\"w-full h-full flex flex-col items-center justify-center text-center p-6\">\n                    <div className=\"bg-white rounded-full p-4 mb-4 shadow-lg\">\n                      <MapPin className=\"text-blue-600\" size={48} />\n                    </div>\n                    <h4 className=\"text-xl font-semibold text-gray-800 mb-2\">公司位置</h4>\n                    <p className=\"text-gray-600 mb-2\">安徽省合肥市蜀山区蜀山经济开发区</p>\n                    <p className=\"text-gray-600 mb-2\">中国(安徽)自由贸易试验区合肥片区</p>\n                    <p className=\"text-gray-600 mb-6\">花峰路1201号跨境电商产业园三期3幢G区8层1326号</p>\n\n                    {/* 地图导航按钮 */}\n                    <div className=\"flex flex-col sm:flex-row gap-3\">\n                      <a\n                        href=\"https://map.baidu.com/search/安徽省合肥市蜀山区花峰路1201号跨境电商产业园三期3幢G区8层1326号\"\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm\"\n                      >\n                        <ExternalLink size={16} className=\"mr-2\" />\n                        百度地图导航\n                      </a>\n                      <a\n                        href=\"https://uri.amap.com/search?query=安徽省合肥市蜀山区花峰路1201号跨境电商产业园三期3幢G区8层1326号\"\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 text-sm\"\n                      >\n                        <ExternalLink size={16} className=\"mr-2\" />\n                        高德地图导航\n                      </a>\n                    </div>\n                  </div>\n\n                  {/* 装饰性地图图标 */}\n                  <div className=\"absolute top-4 left-4 opacity-20\">\n                    <div className=\"w-8 h-8 bg-blue-600 rounded-full\"></div>\n                  </div>\n                  <div className=\"absolute bottom-4 right-4 opacity-20\">\n                    <div className=\"w-6 h-6 bg-green-600 rounded-full\"></div>\n                  </div>\n                  <div className=\"absolute top-1/2 right-8 opacity-20\">\n                    <div className=\"w-4 h-4 bg-purple-600 rounded-full\"></div>\n                  </div>\n                </div>\n\n                {/* 地址信息卡片 */}\n                <div className=\"mt-4 p-4 bg-blue-50 rounded-lg border border-blue-100\">\n                  <div className=\"flex items-start space-x-3\">\n                    <div className=\"bg-blue-600 rounded-full p-2\">\n                      <MapPin className=\"text-white\" size={16} />\n                    </div>\n                    <div className=\"flex-1\">\n                      <h4 className=\"font-semibold text-gray-800 mb-1\">详细地址</h4>\n                      <p className=\"text-gray-600 mb-2\">安徽省合肥市蜀山区蜀山经济开发区中国(安徽)自由贸易试验区合肥片区花峰路1201号跨境电商产业园三期3幢G区8层1326号</p>\n                      <div className=\"flex flex-col sm:flex-row gap-2 text-sm\">\n                        <span className=\"inline-flex items-center text-gray-500\">\n                          📍 导航提示：搜索\"花峰路1201号跨境电商产业园\"\n                        </span>\n                        <span className=\"inline-flex items-center text-gray-500\">\n                          🚗 建议使用导航软件精确定位\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            \n            {/* 联系表单 */}\n            <div>\n              <h2 className=\"text-3xl font-bold text-gray-800 mb-8\">在线咨询</h2>\n              \n              <form onSubmit={handleSubmit} className=\"space-y-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      姓名 *\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"name\"\n                      name=\"name\"\n                      required\n                      value={formData.name}\n                      onChange={handleChange}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      placeholder=\"请输入您的姓名\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <label htmlFor=\"company\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      公司名称\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"company\"\n                      name=\"company\"\n                      value={formData.company}\n                      onChange={handleChange}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      placeholder=\"请输入公司名称\"\n                    />\n                  </div>\n                </div>\n                \n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      联系电话 *\n                    </label>\n                    <input\n                      type=\"tel\"\n                      id=\"phone\"\n                      name=\"phone\"\n                      required\n                      value={formData.phone}\n                      onChange={handleChange}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      placeholder=\"请输入联系电话\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      电子邮箱\n                    </label>\n                    <input\n                      type=\"email\"\n                      id=\"email\"\n                      name=\"email\"\n                      value={formData.email}\n                      onChange={handleChange}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      placeholder=\"请输入电子邮箱\"\n                    />\n                  </div>\n                </div>\n                \n                <div>\n                  <label htmlFor=\"service\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    咨询服务\n                  </label>\n                  <select\n                    id=\"service\"\n                    name=\"service\"\n                    value={formData.service}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    <option value=\"\">请选择咨询服务</option>\n                    <option value=\"management\">企业管理咨询</option>\n                    <option value=\"technology\">技术服务</option>\n                    <option value=\"data\">数据处理服务</option>\n                    <option value=\"consulting\">专业咨询服务</option>\n                    <option value=\"business\">商务代理服务</option>\n                    <option value=\"engineering\">工程建设</option>\n                    <option value=\"sales\">产品销售</option>\n                    <option value=\"digital\">数字内容制作</option>\n                    <option value=\"other\">其他服务</option>\n                  </select>\n                </div>\n                \n                <div>\n                  <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    详细需求 *\n                  </label>\n                  <textarea\n                    id=\"message\"\n                    name=\"message\"\n                    required\n                    rows={5}\n                    value={formData.message}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    placeholder=\"请详细描述您的需求和问题\"\n                  ></textarea>\n                </div>\n                \n                <button\n                  type=\"submit\"\n                  className=\"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center\"\n                >\n                  <Send className=\"mr-2\" size={20} />\n                  提交咨询\n                </button>\n              </form>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,SAAS;QACT,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,eAAe;QACf,QAAQ,GAAG,CAAC,SAAS;QACrB,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC;gCAAE,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;;;;;0BAQvD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAEtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;4DAAgB,MAAM;;;;;;;;;;;kEAE1C,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;0EAC7B,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;0EAC7B,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAIjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;4DAAiB,MAAM;;;;;;;;;;;kEAE1C,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;0EAC7B,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAIjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;4DAAkB,MAAM;;;;;;;;;;;kEAE1C,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;0EAC7B,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAIjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;4DAAkB,MAAM;;;;;;;;;;;kEAE3C,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;0EAC7B,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;kDAMnC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;oEAAgB,MAAM;;;;;;;;;;;0EAE1C,8OAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,8OAAC;gEAAE,WAAU;0EAAqB;;;;;;0EAClC,8OAAC;gEAAE,WAAU;0EAAqB;;;;;;0EAClC,8OAAC;gEAAE,WAAU;0EAAqB;;;;;;0EAGlC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,MAAK;wEACL,QAAO;wEACP,KAAI;wEACJ,WAAU;;0FAEV,8OAAC,sNAAA,CAAA,eAAY;gFAAC,MAAM;gFAAI,WAAU;;;;;;4EAAS;;;;;;;kFAG7C,8OAAC;wEACC,MAAK;wEACL,QAAO;wEACP,KAAI;wEACJ,WAAU;;0FAEV,8OAAC,sNAAA,CAAA,eAAY;gFAAC,MAAM;gFAAI,WAAU;;;;;;4EAAS;;;;;;;;;;;;;;;;;;;kEAOjD,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;;;;;;;;;;kEAEjB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;;;;;;;;;;kEAEjB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;0DAKnB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;gEAAa,MAAM;;;;;;;;;;;sEAEvC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAmC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAAqB;;;;;;8EAClC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAyC;;;;;;sFAGzD,8OAAC;4EAAK,WAAU;sFAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWrE,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAEtD,8OAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAO,WAAU;0EAA+C;;;;;;0EAG/E,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,QAAQ;gEACR,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAU,WAAU;0EAA+C;;;;;;0EAGlF,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,OAAO;gEACvB,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAQ,WAAU;0EAA+C;;;;;;0EAGhF,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,QAAQ;gEACR,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAQ,WAAU;0EAA+C;;;;;;0EAGhF,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAA+C;;;;;;kEAGlF,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAG;;;;;;0EACjB,8OAAC;gEAAO,OAAM;0EAAa;;;;;;0EAC3B,8OAAC;gEAAO,OAAM;0EAAa;;;;;;0EAC3B,8OAAC;gEAAO,OAAM;0EAAO;;;;;;0EACrB,8OAAC;gEAAO,OAAM;0EAAa;;;;;;0EAC3B,8OAAC;gEAAO,OAAM;0EAAW;;;;;;0EACzB,8OAAC;gEAAO,OAAM;0EAAc;;;;;;0EAC5B,8OAAC;gEAAO,OAAM;0EAAQ;;;;;;0EACtB,8OAAC;gEAAO,OAAM;0EAAU;;;;;;0EACxB,8OAAC;gEAAO,OAAM;0EAAQ;;;;;;;;;;;;;;;;;;0DAI1B,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAA+C;;;;;;kEAGlF,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,QAAQ;wDACR,MAAM;wDACN,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;wDAAO,MAAM;;;;;;oDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD", "debugId": null}}, {"offset": {"line": 960, "column": 0}, "map": {"version": 3, "file": "map-pin.js", "sources": ["file:///D:/vscode/www1/yaosen-website/node_modules/lucide-react/src/icons/map-pin.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0',\n      key: '1r0f0z',\n    },\n  ],\n  ['circle', { cx: '12', cy: '10', r: '3', key: 'ilqhr7' }],\n];\n\n/**\n * @component @name MapPin\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTBjMCA0Ljk5My01LjUzOSAxMC4xOTMtNy4zOTkgMTEuNzk5YTEgMSAwIDAgMS0xLjIwMiAwQzkuNTM5IDIwLjE5MyA0IDE0Ljk5MyA0IDEwYTggOCAwIDAgMSAxNiAwIiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTAiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/map-pin\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MapPin = createLucideIcon('map-pin', __iconNode);\n\nexport default MapPin;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "file": "clock.js", "sources": ["file:///D:/vscode/www1/yaosen-website/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['polyline', { points: '12 6 12 12 16 14', key: '68esgv' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxMiA2IDEyIDEyIDE2IDE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1056, "column": 0}, "map": {"version": 3, "file": "send.js", "sources": ["file:///D:/vscode/www1/yaosen-website/node_modules/lucide-react/src/icons/send.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z',\n      key: '1ffxy3',\n    },\n  ],\n  ['path', { d: 'm21.854 2.147-10.94 10.939', key: '12cjpa' }],\n];\n\n/**\n * @component @name Send\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQuNTM2IDIxLjY4NmEuNS41IDAgMCAwIC45MzctLjAyNGw2LjUtMTlhLjQ5Ni40OTYgMCAwIDAtLjYzNS0uNjM1bC0xOSA2LjVhLjUuNSAwIDAgMC0uMDI0LjkzN2w3LjkzIDMuMThhMiAyIDAgMCAxIDEuMTEyIDEuMTF6IiAvPgogIDxwYXRoIGQ9Im0yMS44NTQgMi4xNDctMTAuOTQgMTAuOTM5IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/send\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Send = createLucideIcon('send', __iconNode);\n\nexport default Send;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC7D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1102, "column": 0}, "map": {"version": 3, "file": "external-link.js", "sources": ["file:///D:/vscode/www1/yaosen-website/node_modules/lucide-react/src/icons/external-link.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 3h6v6', key: '1q9fwt' }],\n  ['path', { d: 'M10 14 21 3', key: 'gplh6r' }],\n  ['path', { d: 'M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6', key: 'a6xqqp' }],\n];\n\n/**\n * @component @name ExternalLink\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgM2g2djYiIC8+CiAgPHBhdGggZD0iTTEwIDE0IDIxIDMiIC8+CiAgPHBhdGggZD0iTTE4IDEzdjZhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWOGEyIDIgMCAwIDEgMi0yaDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/external-link\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ExternalLink = createLucideIcon('external-link', __iconNode);\n\nexport default ExternalLink;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4D,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3F;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}
{"recommendations": ["bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-typescript-next", "ms-vscode.vscode-eslint", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-json", "ms-vscode.vscode-css", "ms-vscode.vscode-html", "bradlc.vscode-tailwindcss", "ms-playwright.playwright", "ms-vscode.test-adapter-converter"], "unwantedRecommendations": ["ms-vscode.vscode-typescript", "hookyqr.beautify"]}
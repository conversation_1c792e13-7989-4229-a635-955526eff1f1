{"version": "0.2.0", "configurations": [{"name": "本地开发 + 自动同步", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/next", "args": ["dev"], "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "preLaunchTask": "开始监听同步", "postDebugTask": "停止监听同步"}, {"name": "仅本地开发", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/next", "args": ["dev"], "env": {"NODE_ENV": "development"}, "console": "integratedTerminal"}], "compounds": [{"name": "开发 + 同步", "configurations": ["本地开发 + 自动同步"]}]}
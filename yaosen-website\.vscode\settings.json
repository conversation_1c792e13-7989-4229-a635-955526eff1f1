{"files.watcherExclude": {"**/node_modules/**": true, "**/.next/**": true, "**/dist/**": true, "**/.git/**": true}, "search.exclude": {"**/node_modules": true, "**/.next": true, "**/dist": true}, "files.exclude": {"**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "typescript.preferences.includePackageJsonAutoImports": "auto", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "terminal.integrated.env.osx": {"SYNC_SERVER": "your-server-ip"}, "terminal.integrated.env.linux": {"SYNC_SERVER": "your-server-ip"}, "terminal.integrated.env.windows": {"SYNC_SERVER": "your-server-ip"}}
{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.rulers": [80, 120], "editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": false, "editor.wordWrap": "on", "editor.minimap.enabled": true, "editor.bracketPairColorization.enabled": true, "editor.guides.bracketPairs": true, "typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "typescript.preferences.includePackageJsonAutoImports": "auto", "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "emmet.triggerExpansionOnTab": true, "files.associations": {"*.css": "tailwindcss", "*.scss": "tailwindcss"}, "files.exclude": {"**/node_modules": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/.next": true, "**/out": true, "**/.vercel": true, "**/dist": true}, "files.watcherExclude": {"**/node_modules/**": true, "**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/node_modules/*/**": true, "**/.next/**": true, "**/dist/**": true}, "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/*.code-search": true, "**/.next": true, "**/out": true, "**/.vercel": true, "**/dist": true}, "tailwindCSS.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "eslint.workingDirectories": ["."], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "git.ignoreLimitWarning": true, "git.autofetch": true, "explorer.fileNesting.enabled": true, "explorer.fileNesting.expand": false, "explorer.fileNesting.patterns": {"*.ts": "${capture}.js", "*.js": "${capture}.js.map, ${capture}.min.js, ${capture}.d.ts", "*.jsx": "${capture}.js", "*.tsx": "${capture}.ts", "tsconfig.json": "tsconfig.*.json", "package.json": "package-lock.json, yarn.lock, pnpm-lock.yaml", ".eslintrc.js": ".eslint<PERSON>ore, .eslintrc.*, eslint.config.*", ".prettierrc": ".prettieri<PERSON>re", "tailwind.config.*": "tailwind.config.*, postcss.config.*", "next.config.*": "next-env.d.ts", "README.md": "*.md"}, "terminal.integrated.defaultProfile.windows": "PowerShell", "terminal.integrated.profiles.windows": {"PowerShell": {"source": "PowerShell", "icon": "terminal-powershell"}}, "terminal.integrated.env.osx": {"SYNC_SERVER": "your-server-ip"}, "terminal.integrated.env.linux": {"SYNC_SERVER": "your-server-ip"}, "terminal.integrated.env.windows": {"SYNC_SERVER": "your-server-ip"}, "workbench.colorTheme": "Default Dark+", "workbench.iconTheme": "vs-seti", "workbench.editor.enablePreview": false, "workbench.editor.closeOnFileDelete": true, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.wordWrap": "on", "editor.quickSuggestions": {"comments": "off", "strings": "off", "other": "off"}}}
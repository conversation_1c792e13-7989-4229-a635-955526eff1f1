{"version": "2.0.0", "tasks": [{"label": "同步到服务器", "type": "shell", "command": "./sync-to-server.sh", "args": ["once"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "将本地文件同步到远程服务器"}, {"label": "开始监听同步", "type": "shell", "command": "./sync-to-server.sh", "args": ["watch"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "dedicated", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": [], "detail": "监听文件变化并自动同步到服务器"}, {"label": "配置SSH密钥", "type": "shell", "command": "./sync-to-server.sh", "args": ["setup"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "配置SSH密钥以便无密码连接服务器"}, {"label": "本地开发服务器", "type": "shell", "command": "npm", "args": ["run", "dev"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "dedicated", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": [], "detail": "启动本地开发服务器"}, {"label": "部署到生产环境", "type": "shell", "command": "ssh", "args": ["root@your-server-ip", "cd /opt/yaosen-website && ./deploy.sh prod"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "在远程服务器上部署生产环境"}]}
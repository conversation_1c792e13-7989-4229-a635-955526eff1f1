# 💻 耀森网站开发指南

## 🎯 开发模式

本项目采用**本地开发**模式，提供最佳的开发体验和安全性。

## 🚀 快速开始

### 一键启动
```bash
# Linux/macOS
./start-dev.sh

# Windows  
start-dev.bat
```

### 手动启动
```bash
# 1. 安装依赖
npm install

# 2. 配置环境
./scripts/security-setup.sh

# 3. 启动开发
npm run dev

# 4. 访问 http://localhost:3000
```

## 📋 开发命令

```bash
# 开发相关
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run start        # 启动生产服务器

# 代码质量
npm run lint         # 代码检查
npm run type-check   # 类型检查
npm run test         # 运行测试

# 安全检查
./security-check.sh  # 安全扫描
```

## 🛠️ 开发工具

### VS Code 推荐扩展
- Tailwind CSS IntelliSense
- ES7+ React/Redux/React-Native snippets
- TypeScript Importer
- Prettier - Code formatter
- ESLint

### 开发规范
- **组件**: PascalCase (UserProfile.tsx)
- **文件**: kebab-case (user-profile.tsx)  
- **变量**: camelCase (userName)
- **常量**: UPPER_SNAKE_CASE (API_URL)

## 📁 项目结构

```
yaosen-website/
├── src/
│   ├── app/           # Next.js App Router
│   └── components/    # React 组件
├── public/           # 静态资源
├── scripts/          # 构建脚本
├── .env.local       # 环境变量
└── package.json     # 项目配置
```

## 🔧 环境配置

### 开发环境
```bash
NODE_ENV=development
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

### 生产环境  
```bash
NODE_ENV=production
NEXT_PUBLIC_SITE_URL=https://www.yaosen.com
```

## 🧪 测试指南

```bash
# 单元测试
npm run test

# 端到端测试
npm run test:e2e

# 测试覆盖率
npm run test:coverage
```

## 🚀 部署流程

### 本地测试
```bash
# 构建测试
npm run build
npm run start

# 容器测试 (官方镜像)
./deploy.sh dev-test
```

### 生产部署 (新架构)

#### 🎯 **官方镜像 + 目录映射架构**
```bash
# 首次部署
./security-check.sh          # 安全检查
./deploy-to-remote.sh         # 完整部署

# 后续快速更新
./update-remote.sh            # 只同步代码，无需重建镜像
```

#### 🚀 **新架构优势**
- ✅ **更快的部署** - 无需重新构建镜像
- ✅ **简化维护** - 使用官方Node.js镜像
- ✅ **快速更新** - 上传文件即可更新
- ✅ **降低复杂性** - 减少自定义配置

## 🔍 故障排除

### 常见问题

**端口被占用**
```bash
# 查找进程
lsof -ti:3000
# 杀死进程  
kill -9 <PID>
```

**依赖问题**
```bash
# 清理重装
rm -rf node_modules package-lock.json
npm install
```

**类型错误**
```bash
# 重启TypeScript服务
# VS Code: Ctrl+Shift+P -> "TypeScript: Restart TS Server"
```

## 📚 技术栈

- **框架**: Next.js 15 + React 19
- **语言**: TypeScript
- **样式**: Tailwind CSS 4.0
- **图标**: Lucide React
- **部署**: Docker + 阿里云

## 🤝 贡献指南

### Git 工作流
```bash
# 功能开发
git checkout -b feature/新功能
git commit -m "feat: 添加新功能"

# 问题修复
git checkout -b bugfix/问题描述  
git commit -m "fix: 修复问题"
```

### 代码审查
- 所有PR需要代码审查
- 确保测试通过
- 遵循代码规范

---

**享受高效的本地开发体验！** 🎉

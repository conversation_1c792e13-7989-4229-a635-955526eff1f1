# 🐳 Docker部署架构说明

## 🎯 新架构概览

耀森网站采用**官方镜像 + 目录映射**的全新Docker部署架构，彻底解决了传统自定义镜像的效率问题。

## 🔄 架构对比

### ❌ **旧架构问题 (自定义镜像)**
```
本地代码 → 构建自定义镜像 → 上传镜像 → 部署容器
```
**问题：**
- 🐌 每次部署都要重新构建镜像 (5-10分钟)
- 💾 镜像文件巨大，上传耗时
- 🔧 复杂的多阶段构建配置
- 🚫 无法快速热更新代码

### ✅ **新架构优势 (官方镜像 + 目录映射)**
```
本地代码 → 同步到映射目录 → 重启容器 → 立即生效
```
**优势：**
- ⚡ 快速部署，只需同步文件 (30秒内)
- 🎯 使用官方Node.js镜像，稳定可靠
- 🔄 支持热更新，开发体验极佳
- 🛠️ 简化配置，易于维护

## 🏗️ 技术实现

### Docker Compose 配置
```yaml
# 生产环境 - 使用官方镜像 + 目录映射
yaosen-prod:
  image: node:18-alpine                    # 官方镜像
  working_dir: /app
  volumes:
    - /opt/yaosen-website:/app             # 目录映射
    - /opt/yaosen-website/node_modules:/app/node_modules
  command: sh -c "npm ci --only=production && npm run build && npm start"
```

### 目录结构
```
服务器: /opt/yaosen-website/
├── src/                 # 源代码 (映射)
├── public/              # 静态资源 (映射)
├── package.json         # 项目配置 (映射)
├── node_modules/        # 依赖包 (容器内)
└── .next/              # 构建输出 (容器内)
```

## 🚀 部署流程

### 首次部署
```bash
# 1. 完整部署 (包含环境配置)
./deploy-to-remote.sh

# 执行过程:
# ├── 安装Docker环境
# ├── 创建映射目录 /opt/yaosen-website
# ├── 同步项目文件
# ├── 启动官方Node.js容器
# └── 映射目录到容器内
```

### 日常更新
```bash
# 2. 快速更新 (只同步代码)
./update-remote.sh

# 执行过程:
# ├── 同步修改的文件到 /opt/yaosen-website
# ├── 重启容器 (重新构建和启动应用)
# └── 立即生效 (30秒内完成)
```

## 📊 性能对比

| 操作 | 旧架构 (自定义镜像) | 新架构 (目录映射) | 提升 |
|------|-------------------|------------------|------|
| 首次部署 | 15-20分钟 | 5-8分钟 | **60%+** |
| 代码更新 | 10-15分钟 | 30秒-1分钟 | **95%+** |
| 镜像大小 | 500MB-1GB | 150MB (官方) | **70%+** |
| 配置复杂度 | 高 (Dockerfile) | 低 (docker-compose) | **简化** |

## 🔧 管理命令

### 本地管理
```bash
# 本地开发
./start-dev.sh              # 一键启动本地开发
npm run dev                  # 传统启动方式

# 本地测试
./deploy.sh dev-test         # 容器化开发测试
./deploy.sh prod             # 本地生产环境测试
```

### 远程管理
```bash
# 部署相关
./deploy-to-remote.sh        # 首次完整部署
./update-remote.sh           # 快速更新代码
./update-remote.sh status    # 查看服务器状态

# 服务管理
ssh user@server "cd /opt/yaosen-website && docker-compose restart yaosen-prod"
ssh user@server "cd /opt/yaosen-website && docker-compose logs -f yaosen-prod"
```

## 🛡️ 安全考虑

### 目录权限
```bash
# 映射目录权限设置
sudo chown -R 1000:1000 /opt/yaosen-website
chmod -R 755 /opt/yaosen-website
```

### 容器安全
```yaml
security_opt:
  - no-new-privileges:true    # 禁止权限提升
cap_drop:
  - ALL                       # 移除所有capabilities
user: "1000:1000"            # 非root用户运行
```

### 文件同步安全
```bash
# 排除敏感文件
--exclude '.env.local'
--exclude '.git'
--exclude 'node_modules'
--exclude '*.log'
```

## 🔍 故障排除

### 常见问题

#### 1. 容器启动失败
```bash
# 查看容器日志
docker-compose logs yaosen-prod

# 检查映射目录权限
ls -la /opt/yaosen-website
```

#### 2. 文件同步失败
```bash
# 检查SSH连接
ssh user@server "echo 'test'"

# 手动同步测试
rsync -avz --dry-run ./ user@server:/opt/yaosen-website/
```

#### 3. 应用无法访问
```bash
# 检查容器状态
docker-compose ps

# 检查端口映射
docker-compose port yaosen-prod 3000
```

## 🎯 最佳实践

### 开发工作流
1. **本地开发** - 使用 `./start-dev.sh` 或 `npm run dev`
2. **本地测试** - 使用 `npm run build && npm start`
3. **容器测试** - 使用 `./deploy.sh dev-test`
4. **部署生产** - 使用 `./update-remote.sh`

### 文件管理
- ✅ 只同步必要的源代码文件
- ✅ 排除构建产物和依赖包
- ✅ 使用 `.gitignore` 管理排除列表
- ✅ 定期清理无用文件

### 性能优化
- ✅ 使用 `npm ci --only=production` 安装生产依赖
- ✅ 启用 Next.js 生产优化
- ✅ 配置适当的容器资源限制
- ✅ 使用 Alpine Linux 减少镜像大小

## 🔮 未来扩展

### 多环境支持
```bash
# 开发环境
./update-remote.sh dev

# 测试环境  
./update-remote.sh staging

# 生产环境
./update-remote.sh prod
```

### 自动化CI/CD
```yaml
# GitHub Actions 示例
- name: Deploy to Production
  run: |
    ./update-remote.sh prod
```

### 集群部署
```yaml
# Docker Swarm 或 Kubernetes
# 支持多节点部署和负载均衡
```

---

**新架构让部署变得简单、快速、可靠！** 🚀

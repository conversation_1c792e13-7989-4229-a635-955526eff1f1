# 🔒 耀森网站安全部署指南

## ⚠️ 重要安全提醒

**在部署到生产环境之前，必须完成以下安全配置！**

## 🚀 安全部署步骤

### 第一步：运行安全初始化脚本

```bash
# 运行安全初始化脚本
./scripts/security-setup.sh
```

这个脚本将：
- ✅ 生成所有必要的安全凭据
- ✅ 创建安全的环境变量文件
- ✅ 生成SSH密钥对
- ✅ 设置SSL证书目录
- ✅ 更新.gitignore文件

### 第二步：配置服务器SSH访问

```bash
# 1. 将生成的SSH公钥添加到服务器
ssh-copy-id -i ~/.ssh/yaosen_deploy_key.pub root@your-server-ip

# 2. 测试SSH连接
ssh -i ~/.ssh/yaosen_deploy_key root@your-server-ip

# 3. 添加服务器主机密钥到known_hosts
ssh-keyscan -H your-server-ip >> ~/.ssh/known_hosts
```

### 第三步：获取SSL证书

#### 选项A：使用Let's Encrypt（推荐）
```bash
# 在服务器上安装certbot
apt install certbot python3-certbot-nginx

# 获取SSL证书
certbot --nginx -d your-domain.com

# 设置自动续期
crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

#### 选项B：使用自有证书
```bash
# 将证书文件复制到ssl目录
cp your-cert.pem yaosen-website/ssl/cert.pem
cp your-key.pem yaosen-website/ssl/key.pem

# 设置正确的权限
chmod 600 yaosen-website/ssl/key.pem
chmod 644 yaosen-website/ssl/cert.pem
```

### 第四步：配置环境变量

编辑 `.env.local` 文件，确保所有配置正确：

```bash
# 检查环境变量文件
cat .env.local

# 确保以下配置正确：
# - CODE_SERVER_PASSWORD（强密码）
# - JWT_SECRET（64字符随机字符串）
# - DATABASE_PASSWORD（强密码）
# - 域名配置
```

### 第五步：运行安全检查

```bash
# 运行安全检查脚本
./security-check.sh

# 确保所有检查项都通过
# 安全评分应该 >= 90
```

### 第六步：安全部署

```bash
# 使用安全的部署脚本
./deploy-to-remote.sh

# 或者手动部署
rsync -avz --progress \
    -e "ssh -i ~/.ssh/yaosen_deploy_key -o StrictHostKeyChecking=yes" \
    --exclude-from=.gitignore \
    ./ root@your-server-ip:/opt/yaosen-website/

# 在服务器上启动服务
ssh -i ~/.ssh/yaosen_deploy_key root@your-server-ip \
    "cd /opt/yaosen-website && ./deploy.sh prod"
```

## 🔍 部署后安全验证

### 1. SSL/TLS检查
```bash
# 检查SSL配置
curl -I https://your-domain.com

# 使用SSL Labs测试
# 访问：https://www.ssllabs.com/ssltest/
```

### 2. 安全头检查
```bash
# 检查安全响应头
curl -I https://your-domain.com

# 应该包含以下头部：
# - Strict-Transport-Security
# - X-Frame-Options: DENY
# - X-Content-Type-Options: nosniff
# - X-XSS-Protection: 1; mode=block
```

### 3. 端口扫描检查
```bash
# 检查开放端口
nmap -sS your-server-ip

# 应该只开放：22 (SSH), 80 (HTTP), 443 (HTTPS)
```

### 4. 服务状态检查
```bash
# 检查Docker容器状态
ssh root@your-server-ip "docker ps"

# 检查Nginx状态
ssh root@your-server-ip "systemctl status nginx"
```

## 🚨 安全事件响应

### 发现安全问题时的处理步骤

1. **立即隔离**
   ```bash
   # 停止服务
   ssh root@your-server-ip "cd /opt/yaosen-website && ./deploy.sh stop"
   
   # 阻止可疑IP
   ssh root@your-server-ip "ufw deny from suspicious-ip"
   ```

2. **评估影响**
   ```bash
   # 检查日志
   ssh root@your-server-ip "tail -100 /var/log/nginx/access.log"
   ssh root@your-server-ip "tail -100 /var/log/auth.log"
   ```

3. **修复问题**
   ```bash
   # 更新密码
   ./scripts/security-setup.sh
   
   # 重新部署
   ./deploy-to-remote.sh
   ```

## 📋 安全维护清单

### 每日检查
- [ ] 检查系统日志
- [ ] 监控异常访问
- [ ] 验证服务状态

### 每周检查
- [ ] 运行安全扫描
- [ ] 检查SSL证书状态
- [ ] 更新系统补丁

### 每月检查
- [ ] 更换临时密码
- [ ] 审查访问日志
- [ ] 备份验证

### 每季度检查
- [ ] 更换所有密钥
- [ ] 安全审计
- [ ] 渗透测试

## 🔧 安全工具推荐

### 服务器安全
```bash
# 安装fail2ban防止暴力破解
apt install fail2ban

# 安装rkhunter检测rootkit
apt install rkhunter

# 安装lynis进行安全审计
apt install lynis
```

### 监控工具
```bash
# 安装htop监控系统资源
apt install htop

# 安装iftop监控网络流量
apt install iftop

# 安装logwatch分析日志
apt install logwatch
```

## 📞 紧急联系信息

- **技术负责人**：[联系方式]
- **安全团队**：[联系方式]
- **服务器提供商**：[联系方式]

## 🎯 安全目标

- 🔒 **机密性**：保护敏感数据不被未授权访问
- 🛡️ **完整性**：确保数据不被恶意修改
- ⚡ **可用性**：保证服务的持续可用
- 📊 **可审计性**：记录所有重要操作
- 🔄 **可恢复性**：快速从安全事件中恢复

---

**记住：安全是一个持续的过程，不是一次性的任务！**

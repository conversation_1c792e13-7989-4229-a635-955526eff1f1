@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 耀森网站远程部署脚本 (Windows版本)
:: 一键部署到远程Docker服务器

echo.
echo ==================================================
echo     耀森企业管理有限公司网站远程部署脚本
echo ==================================================
echo.
echo 此脚本将帮助您将网站部署到远程Docker服务器
echo.

:: 检查依赖
echo [检查] 检查必要工具...
where ssh >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到SSH工具，请安装Git for Windows或OpenSSH
    echo 下载地址: https://git-scm.com/download/win
    pause
    exit /b 1
)

where scp >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到SCP工具，请安装Git for Windows或OpenSSH
    pause
    exit /b 1
)

echo [成功] 工具检查完成
echo.

:: 获取服务器信息
echo [输入] 请输入服务器信息:
set /p SERVER_IP="服务器IP地址: "
if "!SERVER_IP!"=="" (
    echo [错误] 服务器IP不能为空
    pause
    exit /b 1
)

set /p SERVER_USER="SSH用户名 [默认: root]: "
if "!SERVER_USER!"=="" set SERVER_USER=root

set /p SERVER_PATH="远程部署路径 [默认: /opt/yaosen-website]: "
if "!SERVER_PATH!"=="" set SERVER_PATH=/opt/yaosen-website

echo.
echo [信息] 配置信息:
echo   服务器: !SERVER_USER!@!SERVER_IP!
echo   路径: !SERVER_PATH!
echo.

set /p CONFIRM="确认以上信息正确吗? [y/N]: "
if /i not "!CONFIRM!"=="y" (
    echo [取消] 部署已取消
    pause
    exit /b 1
)

:: 测试连接
echo.
echo [步骤1/5] 测试服务器连接...
ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no !SERVER_USER!@!SERVER_IP! "echo 'Connection test successful'" >nul 2>&1
if errorlevel 1 (
    echo [错误] 无法连接到服务器，请检查IP、用户名和SSH密钥配置
    pause
    exit /b 1
)
echo [成功] 服务器连接正常

:: 创建远程目录
echo.
echo [步骤2/5] 创建远程目录...
ssh -o StrictHostKeyChecking=no !SERVER_USER!@!SERVER_IP! "mkdir -p !SERVER_PATH!"
echo [成功] 远程目录创建完成

:: 上传项目文件
echo.
echo [步骤3/5] 上传项目文件...
echo [信息] 正在上传文件，请稍候...

:: 创建临时排除文件列表
echo node_modules/ > .rsync_exclude
echo .next/ >> .rsync_exclude
echo .git/ >> .rsync_exclude
echo dist/ >> .rsync_exclude
echo *.log >> .rsync_exclude
echo .env.local >> .rsync_exclude
echo .DS_Store >> .rsync_exclude
echo Thumbs.db >> .rsync_exclude

:: 使用scp上传文件（Windows下的简化版本）
for %%f in (*.json *.js *.ts *.tsx *.md *.yml *.yaml *.sh *.bat *.conf) do (
    scp -o StrictHostKeyChecking=no "%%f" !SERVER_USER!@!SERVER_IP!:!SERVER_PATH!/ >nul 2>&1
)

:: 上传src目录
scp -r -o StrictHostKeyChecking=no src !SERVER_USER!@!SERVER_IP!:!SERVER_PATH!/ >nul 2>&1
:: 上传public目录
scp -r -o StrictHostKeyChecking=no public !SERVER_USER!@!SERVER_IP!:!SERVER_PATH!/ >nul 2>&1

del .rsync_exclude >nul 2>&1

echo [成功] 项目文件上传完成

:: 安装Docker环境
echo.
echo [步骤4/5] 检查并安装Docker环境...
ssh -o StrictHostKeyChecking=no !SERVER_USER!@!SERVER_IP! "
    # 检查Docker是否已安装
    if ! command -v docker &> /dev/null; then
        echo '安装Docker...'
        curl -fsSL https://get.docker.com -o get-docker.sh
        sh get-docker.sh
        systemctl start docker
        systemctl enable docker
        echo 'Docker安装完成'
    else
        echo 'Docker已安装'
    fi
    
    # 检查Docker Compose是否已安装
    if ! command -v docker-compose &> /dev/null; then
        echo '安装Docker Compose...'
        curl -L \"https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)\" -o /usr/local/bin/docker-compose
        chmod +x /usr/local/bin/docker-compose
        echo 'Docker Compose安装完成'
    else
        echo 'Docker Compose已安装'
    fi
"
echo [成功] Docker环境准备完成

:: 部署应用
echo.
echo [步骤5/5] 部署应用...
ssh -o StrictHostKeyChecking=no !SERVER_USER!@!SERVER_IP! "
    cd !SERVER_PATH!
    chmod +x deploy.sh
    ./deploy.sh stop 2>/dev/null || true
    ./deploy.sh prod
"
echo [成功] 应用部署完成

:: 配置防火墙
echo.
echo [配置] 配置防火墙规则...
ssh -o StrictHostKeyChecking=no !SERVER_USER!@!SERVER_IP! "
    if command -v ufw &> /dev/null; then
        ufw allow 22/tcp
        ufw allow 80/tcp
        ufw allow 443/tcp
        echo 'y' | ufw enable 2>/dev/null || true
        echo '防火墙配置完成'
    elif command -v firewall-cmd &> /dev/null; then
        firewall-cmd --permanent --add-port=22/tcp
        firewall-cmd --permanent --add-port=80/tcp
        firewall-cmd --permanent --add-port=443/tcp
        firewall-cmd --reload
        echo '防火墙配置完成'
    else
        echo '未找到防火墙管理工具，请手动配置'
    fi
"

:: 获取服务器公网IP
echo.
echo [获取] 获取服务器公网IP...
for /f %%i in ('ssh -o StrictHostKeyChecking=no !SERVER_USER!@!SERVER_IP! "curl -s ifconfig.me 2>/dev/null || echo !SERVER_IP!"') do set PUBLIC_IP=%%i

:: 显示结果
echo.
echo ==================================================
echo               部署完成！
echo ==================================================
echo.
echo 访问地址: http://!PUBLIC_IP!
echo.
echo 常用管理命令:
echo   查看日志: ssh !SERVER_USER!@!SERVER_IP! "cd !SERVER_PATH! && ./deploy.sh logs"
echo   重启服务: ssh !SERVER_USER!@!SERVER_IP! "cd !SERVER_PATH! && ./deploy.sh restart"
echo   停止服务: ssh !SERVER_USER!@!SERVER_IP! "cd !SERVER_PATH! && ./deploy.sh stop"
echo.

pause

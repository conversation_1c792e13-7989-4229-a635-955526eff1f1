#!/bin/bash

# 耀森网站远程部署脚本 (官方镜像 + 目录映射)
# 一键部署到远程Docker服务器，使用官方Node.js镜像
# 使用方法: ./deploy-to-remote.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "  耀森企业管理有限公司网站远程部署脚本"
    echo "        (官方镜像 + 目录映射架构)"
    echo "=================================================="
    echo -e "${NC}"
    echo "🚀 新架构特点:"
    echo "  • 使用官方Node.js镜像，无需自定义构建"
    echo "  • 通过目录映射部署，更新代码更快速"
    echo "  • 一次配置，持续部署"
    echo ""
}

# 获取服务器信息
get_server_info() {
    echo -e "${YELLOW}请输入服务器信息:${NC}"
    
    # 获取服务器IP
    while [ -z "$SERVER_IP" ]; do
        read -p "服务器IP地址: " SERVER_IP
        if [ -z "$SERVER_IP" ]; then
            log_error "服务器IP不能为空"
        fi
    done
    
    # 获取用户名
    read -p "SSH用户名 [默认: root]: " SERVER_USER
    SERVER_USER=${SERVER_USER:-root}
    
    # 获取密码或选择密钥认证
    echo ""
    echo "选择认证方式:"
    echo "1) 密码认证"
    echo "2) SSH密钥认证"
    read -p "请选择 [1-2]: " AUTH_METHOD
    
    case $AUTH_METHOD in
        1)
            read -s -p "SSH密码: " SERVER_PASSWORD
            echo ""
            USE_PASSWORD=true
            ;;
        2)
            USE_PASSWORD=false
            log_info "将使用SSH密钥认证"
            ;;
        *)
            log_error "无效选择"
            exit 1
            ;;
    esac
    
    # 设置远程路径
    read -p "远程部署路径 [默认: /opt/yaosen-website]: " SERVER_PATH
    SERVER_PATH=${SERVER_PATH:-/opt/yaosen-website}
    
    echo ""
    log_info "配置信息:"
    echo "  服务器: $SERVER_USER@$SERVER_IP"
    echo "  路径: $SERVER_PATH"
    echo "  认证: $([ "$USE_PASSWORD" = true ] && echo "密码" || echo "SSH密钥")"
    echo ""
    
    read -p "确认以上信息正确吗? [y/N]: " CONFIRM
    if [[ ! $CONFIRM =~ ^[Yy]$ ]]; then
        log_error "部署已取消"
        exit 1
    fi
}

# 测试服务器连接
test_connection() {
    log_step "测试服务器连接..."

    # 安全的SSH连接选项
    local ssh_opts="-o ConnectTimeout=10 -o StrictHostKeyChecking=ask -o UserKnownHostsFile=~/.ssh/known_hosts"

    if [ "$USE_PASSWORD" = true ]; then
        # 使用sshpass进行密码认证
        if ! command -v sshpass &> /dev/null; then
            log_error "需要安装sshpass来支持密码认证"
            echo "Ubuntu/Debian: sudo apt install sshpass"
            echo "macOS: brew install sshpass"
            exit 1
        fi

        log_warning "使用密码认证存在安全风险，建议使用SSH密钥认证"

        if sshpass -p "$SERVER_PASSWORD" ssh $ssh_opts $SERVER_USER@$SERVER_IP "echo 'Connection test successful'" 2>/dev/null; then
            log_success "服务器连接正常"
        else
            log_error "无法连接到服务器，请检查IP、用户名和密码"
            log_info "首次连接可能需要确认主机密钥，请手动连接一次：ssh $SERVER_USER@$SERVER_IP"
            exit 1
        fi
    else
        # 使用SSH密钥认证（推荐）
        if ssh $ssh_opts $SERVER_USER@$SERVER_IP "echo 'Connection test successful'" 2>/dev/null; then
            log_success "服务器连接正常"
        else
            log_error "无法连接到服务器，请检查SSH密钥配置"
            log_info "首次连接可能需要确认主机密钥，请手动连接一次：ssh $SERVER_USER@$SERVER_IP"
            log_info "或添加主机密钥：ssh-keyscan -H $SERVER_IP >> ~/.ssh/known_hosts"
            exit 1
        fi
    fi
}

# 执行远程命令
execute_remote() {
    local command="$1"
    if [ "$USE_PASSWORD" = true ]; then
        sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_IP "$command"
    else
        ssh -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_IP "$command"
    fi
}

# 上传文件
upload_files() {
    local local_path="$1"
    local remote_path="$2"
    if [ "$USE_PASSWORD" = true ]; then
        sshpass -p "$SERVER_PASSWORD" rsync -avz --progress \
            -e "ssh -o StrictHostKeyChecking=no" \
            --exclude 'node_modules' \
            --exclude '.next' \
            --exclude '.git' \
            --exclude 'dist' \
            --exclude '*.log' \
            --exclude '.env.local' \
            --exclude '.DS_Store' \
            --exclude 'Thumbs.db' \
            "$local_path/" $SERVER_USER@$SERVER_IP:"$remote_path/"
    else
        rsync -avz --progress \
            -e "ssh -o StrictHostKeyChecking=no" \
            --exclude 'node_modules' \
            --exclude '.next' \
            --exclude '.git' \
            --exclude 'dist' \
            --exclude '*.log' \
            --exclude '.env.local' \
            --exclude '.DS_Store' \
            --exclude 'Thumbs.db' \
            "$local_path/" $SERVER_USER@$SERVER_IP:"$remote_path/"
    fi
}

# 安装Docker环境
install_docker() {
    log_step "检查并安装Docker环境..."

    execute_remote "
        # 检查Docker是否已安装
        if ! command -v docker &> /dev/null; then
            echo '安装Docker...'

            # 安全的Docker安装方法
            # 1. 更新包索引
            apt-get update

            # 2. 安装必要的包
            apt-get install -y apt-transport-https ca-certificates curl gnupg lsb-release

            # 3. 添加Docker官方GPG密钥
            curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

            # 4. 设置稳定版仓库
            echo \"deb [arch=\$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu \$(lsb_release -cs) stable\" | tee /etc/apt/sources.list.d/docker.list > /dev/null

            # 5. 安装Docker Engine
            apt-get update
            apt-get install -y docker-ce docker-ce-cli containerd.io

            systemctl start docker
            systemctl enable docker
            echo 'Docker安装完成'
        else
            echo 'Docker已安装'
        fi

        # 检查Docker Compose是否已安装
        if ! command -v docker-compose &> /dev/null; then
            echo '安装Docker Compose...'

            # 安全的Docker Compose安装
            COMPOSE_VERSION=\$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep 'tag_name' | cut -d'\"' -f4)
            curl -L \"https://github.com/docker/compose/releases/download/\${COMPOSE_VERSION}/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose

            # 验证下载的文件
            if [ -f /usr/local/bin/docker-compose ]; then
                chmod +x /usr/local/bin/docker-compose
                echo 'Docker Compose安装完成'
            else
                echo 'Docker Compose下载失败'
                exit 1
            fi
        else
            echo 'Docker Compose已安装'
        fi

        # 验证安装
        docker --version
        docker-compose --version
    "

    log_success "Docker环境准备完成"
}

# 上传项目文件
upload_project() {
    log_step "上传项目文件到服务器..."
    
    # 创建远程目录
    execute_remote "mkdir -p $SERVER_PATH"
    
    # 上传文件
    upload_files "." "$SERVER_PATH"
    
    log_success "项目文件上传完成"
}

# 部署应用 (官方镜像 + 目录映射)
deploy_application() {
    log_step "部署应用到Docker容器 (官方镜像 + 目录映射)..."

    execute_remote "
        cd $SERVER_PATH

        # 给脚本执行权限
        chmod +x deploy.sh

        # 停止现有容器
        ./deploy.sh stop 2>/dev/null || true

        # 部署生产环境 (使用官方镜像)
        ./deploy.sh prod

        echo '✅ 部署完成！'
        echo '📝 后续更新代码只需:'
        echo '   1. 上传文件到 $SERVER_PATH'
        echo '   2. 运行 ./deploy.sh restart'
        echo '   无需重新构建镜像！'
    "

    log_success "应用部署完成"
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙规则..."
    
    execute_remote "
        # 检查是否有ufw
        if command -v ufw &> /dev/null; then
            ufw allow 22/tcp
            ufw allow 80/tcp
            ufw allow 443/tcp
            echo 'y' | ufw enable 2>/dev/null || true
            echo '防火墙配置完成'
        elif command -v firewall-cmd &> /dev/null; then
            firewall-cmd --permanent --add-port=22/tcp
            firewall-cmd --permanent --add-port=80/tcp
            firewall-cmd --permanent --add-port=443/tcp
            firewall-cmd --reload
            echo '防火墙配置完成'
        else
            echo '未找到防火墙管理工具，请手动配置'
        fi
    "
}

# 获取服务器公网IP
get_server_ip() {
    log_step "获取服务器公网IP..."
    PUBLIC_IP=$(execute_remote "curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || echo '$SERVER_IP'")
    log_info "服务器公网IP: $PUBLIC_IP"
}

# 显示部署结果
show_result() {
    echo ""
    echo -e "${GREEN}"
    echo "=================================================="
    echo "              部署完成！"
    echo "=================================================="
    echo -e "${NC}"
    echo -e "${BLUE}访问地址: http://$PUBLIC_IP${NC}"
    echo -e "${BLUE}管理命令: ssh $SERVER_USER@$SERVER_IP 'cd $SERVER_PATH && ./deploy.sh [prod|stop|logs|restart]'${NC}"
    echo ""
    echo -e "${YELLOW}常用管理命令:${NC}"
    echo "  查看日志: ssh $SERVER_USER@$SERVER_IP 'cd $SERVER_PATH && ./deploy.sh logs'"
    echo "  重启服务: ssh $SERVER_USER@$SERVER_IP 'cd $SERVER_PATH && ./deploy.sh restart'"
    echo "  停止服务: ssh $SERVER_USER@$SERVER_IP 'cd $SERVER_PATH && ./deploy.sh stop'"
    echo ""
}

# 主函数
main() {
    show_welcome
    get_server_info
    test_connection
    install_docker
    upload_project
    deploy_application
    configure_firewall
    get_server_ip
    show_result
}

# 检查依赖
check_dependencies() {
    local missing_deps=()
    
    if ! command -v rsync &> /dev/null; then
        missing_deps+=("rsync")
    fi
    
    if ! command -v ssh &> /dev/null; then
        missing_deps+=("ssh")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_error "缺少依赖: ${missing_deps[*]}"
        echo "请安装缺少的依赖后重试"
        exit 1
    fi
}

# 脚本入口
check_dependencies
main "$@"

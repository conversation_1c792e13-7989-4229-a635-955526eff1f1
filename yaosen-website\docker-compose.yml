version: '3.8'

services:
  # 耀森网站生产环境 - 官方镜像 + 目录映射
  yaosen-prod:
    image: node:18-alpine
    container_name: yaosen-website
    working_dir: /app
    ports:
      - "80:3000"
    volumes:
      - /opt/yaosen-website:/app
      - yaosen-node-modules:/app/node_modules
    environment:
      - NODE_ENV=production
      - PORT=3000
      - TZ=Asia/Shanghai
    command: sh -c "npm ci --only=production && npm run build && npm start"
    restart: unless-stopped
    # 安全配置
    user: "1000:1000"
    security_opt:
      - no-new-privileges:true


volumes:
  yaosen-node-modules:
    name: yaosen-node-modules

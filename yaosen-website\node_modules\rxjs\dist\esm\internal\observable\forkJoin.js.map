{"version": 3, "file": "forkJoin.js", "sourceRoot": "", "sources": ["../../../../src/internal/observable/forkJoin.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAE3C,OAAO,EAAE,oBAAoB,EAAE,MAAM,8BAA8B,CAAC;AACpE,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,iBAAiB,EAAE,MAAM,cAAc,CAAC;AACjD,OAAO,EAAE,wBAAwB,EAAE,MAAM,iCAAiC,CAAC;AAC3E,OAAO,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AAC5D,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AAyIpD,MAAM,UAAU,QAAQ,CAAC,GAAG,IAAW;IACrC,MAAM,cAAc,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC/C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAC3D,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,CAAC,UAAU,EAAE,EAAE;QAC3C,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAC3B,IAAI,CAAC,MAAM,EAAE;YACX,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,OAAO;SACR;QACD,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;QACjC,IAAI,oBAAoB,GAAG,MAAM,CAAC;QAClC,IAAI,kBAAkB,GAAG,MAAM,CAAC;QAChC,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,MAAM,EAAE,WAAW,EAAE,EAAE;YAC7D,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CACvC,wBAAwB,CACtB,UAAU,EACV,CAAC,KAAK,EAAE,EAAE;gBACR,IAAI,CAAC,QAAQ,EAAE;oBACb,QAAQ,GAAG,IAAI,CAAC;oBAChB,kBAAkB,EAAE,CAAC;iBACtB;gBACD,MAAM,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC;YAC9B,CAAC,EACD,GAAG,EAAE,CAAC,oBAAoB,EAAE,EAC5B,SAAS,EACT,GAAG,EAAE;gBACH,IAAI,CAAC,oBAAoB,IAAI,CAAC,QAAQ,EAAE;oBACtC,IAAI,CAAC,kBAAkB,EAAE;wBACvB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;qBAC7D;oBACD,UAAU,CAAC,QAAQ,EAAE,CAAC;iBACvB;YACH,CAAC,CACF,CACF,CAAC;SACH;IACH,CAAC,CAAC,CAAC;IACH,OAAO,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AACjF,CAAC"}
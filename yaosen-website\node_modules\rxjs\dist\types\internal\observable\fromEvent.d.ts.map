{"version": 3, "file": "fromEvent.d.ts", "sourceRoot": "", "sources": ["../../../../src/internal/observable/fromEvent.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAW3C,MAAM,WAAW,qBAAqB;IACpC,WAAW,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM,EAAE,OAAO,EAAE,gBAAgB,GAAG,IAAI,CAAC;IACzE,cAAc,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM,EAAE,OAAO,EAAE,gBAAgB,GAAG,IAAI,CAAC;CAC7E;AAED,oBAAY,gBAAgB,GAAG,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;AAKxD,MAAM,WAAW,0BAA0B;IACzC,WAAW,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;IACrE,cAAc,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;CACzE;AAID,MAAM,WAAW,uBAAuB,CAAC,QAAQ,EAAE,CAAC;IAClD,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;IACpF,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;CACtF;AAED,MAAM,WAAW,mBAAmB,CAAC,CAAC;IACpC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;CAC3B;AAED,MAAM,WAAW,uBAAuB,CAAC,CAAC;IACxC,gBAAgB,CACd,IAAI,EAAE,MAAM,EACZ,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,IAAI,CAAC,GAAG,mBAAmB,CAAC,CAAC,CAAC,GAAG,IAAI,EAC5D,OAAO,CAAC,EAAE,OAAO,GAAG,uBAAuB,GAC1C,IAAI,CAAC;IACR,mBAAmB,CACjB,IAAI,EAAE,MAAM,EACZ,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,IAAI,CAAC,GAAG,mBAAmB,CAAC,CAAC,CAAC,GAAG,IAAI,EAC5D,OAAO,CAAC,EAAE,oBAAoB,GAAG,OAAO,GACvC,IAAI,CAAC;CACT;AAED,MAAM,WAAW,oBAAoB;IACnC,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,IAAI,CAAC,EAAE,OAAO,CAAC;CAChB;AAED,MAAM,WAAW,uBAAwB,SAAQ,oBAAoB;IACnE,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,OAAO,CAAC,EAAE,OAAO,CAAC;CACnB;AAED,wBAAgB,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE,uBAAuB,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAC3I,wBAAgB,SAAS,CAAC,CAAC,EAAE,CAAC,EAC5B,MAAM,EAAE,uBAAuB,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,EAC1E,SAAS,EAAE,MAAM,EACjB,cAAc,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAC9B,UAAU,CAAC,CAAC,CAAC,CAAC;AACjB,wBAAgB,SAAS,CAAC,CAAC,EACzB,MAAM,EAAE,uBAAuB,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,EAC1E,SAAS,EAAE,MAAM,EACjB,OAAO,EAAE,oBAAoB,GAC5B,UAAU,CAAC,CAAC,CAAC,CAAC;AACjB,wBAAgB,SAAS,CAAC,CAAC,EAAE,CAAC,EAC5B,MAAM,EAAE,uBAAuB,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,EAC1E,SAAS,EAAE,MAAM,EACjB,OAAO,EAAE,oBAAoB,EAC7B,cAAc,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAC9B,UAAU,CAAC,CAAC,CAAC,CAAC;AAEjB,wBAAgB,SAAS,CAAC,MAAM,EAAE,qBAAqB,GAAG,SAAS,CAAC,qBAAqB,CAAC,EAAE,SAAS,EAAE,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC;AACpI,0IAA0I;AAC1I,wBAAgB,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE,qBAAqB,GAAG,SAAS,CAAC,qBAAqB,CAAC,EAAE,SAAS,EAAE,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACjI,wBAAgB,SAAS,CAAC,CAAC,EACzB,MAAM,EAAE,qBAAqB,GAAG,SAAS,CAAC,qBAAqB,CAAC,EAChE,SAAS,EAAE,MAAM,EACjB,cAAc,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,GACpC,UAAU,CAAC,CAAC,CAAC,CAAC;AAEjB,wBAAgB,SAAS,CACvB,MAAM,EAAE,0BAA0B,GAAG,SAAS,CAAC,0BAA0B,CAAC,EAC1E,SAAS,EAAE,MAAM,GAChB,UAAU,CAAC,OAAO,CAAC,CAAC;AACvB,0IAA0I;AAC1I,wBAAgB,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE,0BAA0B,GAAG,SAAS,CAAC,0BAA0B,CAAC,EAAE,SAAS,EAAE,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAC3I,wBAAgB,SAAS,CAAC,CAAC,EACzB,MAAM,EAAE,0BAA0B,GAAG,SAAS,CAAC,0BAA0B,CAAC,EAC1E,SAAS,EAAE,MAAM,EACjB,cAAc,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,GACpC,UAAU,CAAC,CAAC,CAAC,CAAC;AAEjB,wBAAgB,SAAS,CAAC,CAAC,EACzB,MAAM,EAAE,uBAAuB,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,uBAAuB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EACpF,SAAS,EAAE,MAAM,GAChB,UAAU,CAAC,CAAC,CAAC,CAAC;AACjB,wBAAgB,SAAS,CAAC,CAAC,EAAE,CAAC,EAC5B,MAAM,EAAE,uBAAuB,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,uBAAuB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EACpF,SAAS,EAAE,MAAM,EACjB,cAAc,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,GAC9C,UAAU,CAAC,CAAC,CAAC,CAAC"}
{"name": "zhuizhu-tech-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "docker:dev": "docker-compose --profile dev up --build", "docker:prod": "docker-compose --profile prod up --build -d", "docker:stop": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:clean": "docker system prune -f", "sync:once": "./sync-to-server.sh once", "sync:watch": "./sync-to-server.sh watch", "sync:setup": "./sync-to-server.sh setup", "dev:sync": "concurrently \"npm run dev\" \"npm run sync:watch\"", "deploy:prod": "npm run sync:once && ssh root@your-server-ip 'cd /opt/yaosen-website && ./deploy.sh prod'"}, "dependencies": {"lucide-react": "^0.513.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^8.2.2", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}
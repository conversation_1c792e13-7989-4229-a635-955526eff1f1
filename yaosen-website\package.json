{"name": "yaosen-enterprise-management-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "pre-commit": "lint-staged", "security-audit": "npm audit --audit-level moderate", "dependency-check": "npm outdated", "build-analyze": "ANALYZE=true npm run build", "docker:dev": "docker-compose --profile dev up --build", "docker:prod": "docker-compose --profile prod up --build -d", "docker:stop": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:clean": "docker system prune -f", "sync:once": "./sync-to-server.sh once", "sync:watch": "./sync-to-server.sh watch", "sync:setup": "./sync-to-server.sh setup", "dev:sync": "concurrently \"npm run dev\" \"npm run sync:watch\"", "deploy:prod": "npm run sync:once && ssh root@your-server-ip 'cd /opt/yaosen-website && ./deploy.sh prod'"}, "dependencies": {"lucide-react": "^0.513.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "clsx": "^2.1.0", "tailwind-merge": "^2.2.0", "zod": "^3.22.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/jest": "^29.5.12", "@testing-library/react": "^14.2.1", "@testing-library/jest-dom": "^6.4.2", "@testing-library/user-event": "^14.5.2", "concurrently": "^8.2.2", "eslint": "^9", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^9.1.0", "husky": "^9.0.11", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.2", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^4", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,scss,md}": ["prettier --write"]}}
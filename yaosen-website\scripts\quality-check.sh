#!/bin/bash

# 代码质量检查脚本
# <AUTHOR>
# @created 2024-01-15

set -e

echo "🔍 开始代码质量检查..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查函数
check_step() {
    local step_name="$1"
    local command="$2"
    
    echo -e "${BLUE}📋 检查: ${step_name}${NC}"
    
    if eval "$command"; then
        echo -e "${GREEN}✅ ${step_name} 通过${NC}"
        return 0
    else
        echo -e "${RED}❌ ${step_name} 失败${NC}"
        return 1
    fi
}

# 初始化错误计数
error_count=0

# 1. TypeScript 类型检查
if ! check_step "TypeScript 类型检查" "npm run type-check"; then
    ((error_count++))
fi

echo ""

# 2. ESLint 代码规范检查
if ! check_step "ESLint 代码规范检查" "npm run lint"; then
    ((error_count++))
fi

echo ""

# 3. Prettier 代码格式检查
if ! check_step "Prettier 代码格式检查" "npm run format:check"; then
    ((error_count++))
    echo -e "${YELLOW}💡 提示: 运行 'npm run format' 自动修复格式问题${NC}"
fi

echo ""

# 4. 单元测试
if ! check_step "单元测试" "npm run test -- --passWithNoTests --watchAll=false"; then
    ((error_count++))
fi

echo ""

# 5. 测试覆盖率检查
if ! check_step "测试覆盖率检查" "npm run test:coverage -- --passWithNoTests --watchAll=false"; then
    ((error_count++))
fi

echo ""

# 6. 安全漏洞检查
if ! check_step "安全漏洞检查" "npm run security-audit"; then
    ((error_count++))
fi

echo ""

# 7. 依赖更新检查
echo -e "${BLUE}📋 检查: 依赖更新状态${NC}"
if npm run dependency-check; then
    echo -e "${GREEN}✅ 依赖检查完成${NC}"
else
    echo -e "${YELLOW}⚠️  有可用的依赖更新${NC}"
fi

echo ""

# 8. 构建测试
if ! check_step "构建测试" "npm run build"; then
    ((error_count++))
fi

echo ""

# 总结报告
echo "📊 质量检查报告"
echo "=================="

if [ $error_count -eq 0 ]; then
    echo -e "${GREEN}🎉 所有检查都通过了！代码质量良好。${NC}"
    exit 0
else
    echo -e "${RED}❌ 发现 $error_count 个问题需要修复。${NC}"
    echo ""
    echo "修复建议："
    echo "1. 查看上面的错误信息"
    echo "2. 运行相应的修复命令"
    echo "3. 重新运行质量检查"
    echo ""
    echo "常用修复命令："
    echo "- npm run lint:fix    # 自动修复 ESLint 问题"
    echo "- npm run format      # 自动修复格式问题"
    echo "- npm test            # 运行测试并查看详细信息"
    exit 1
fi

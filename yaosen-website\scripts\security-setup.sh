#!/bin/bash

# 耀森网站安全初始化脚本
# 用于生成和配置所有必要的安全凭据

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 显示标题
show_header() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "         耀森网站安全初始化工具"
    echo "=================================================="
    echo -e "${NC}"
    echo "此工具将帮助您生成和配置所有必要的安全凭据"
    echo ""
}

# 生成强密码
generate_password() {
    local length=${1:-32}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# 生成JWT密钥
generate_jwt_secret() {
    openssl rand -hex 64
}

# 生成加密密钥
generate_encryption_key() {
    openssl rand -hex 32
}

# 创建安全的.env文件
create_secure_env() {
    log_step "创建安全的环境变量文件..."
    
    local env_file=".env.local"
    
    if [ -f "$env_file" ]; then
        log_warning "环境变量文件已存在，创建备份..."
        cp "$env_file" "$env_file.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    log_info "生成安全凭据..."
    
    # 生成各种密钥和密码
    local code_server_password=$(generate_password 24)
    local jwt_secret=$(generate_jwt_secret)
    local session_secret=$(generate_password 48)
    local encryption_key=$(generate_encryption_key)
    local db_password=$(generate_password 32)
    local redis_password=$(generate_password 24)
    
    # 创建.env文件
    cat > "$env_file" << EOF
# 耀森企业管理有限公司 - 安全环境变量配置
# 生成时间: $(date)
# 警告: 此文件包含敏感信息，请勿提交到版本控制系统

# ===========================================
# 应用基础配置
# ===========================================
NODE_ENV=production
PORT=3000
NEXT_PUBLIC_APP_NAME=耀森企业管理有限公司
NEXT_PUBLIC_APP_VERSION=1.0.0

# ===========================================
# 安全配置
# ===========================================
# 在线代码编辑器密码
CODE_SERVER_PASSWORD=$code_server_password

# JWT密钥（用于身份验证）
JWT_SECRET=$jwt_secret

# 会话密钥（用于会话管理）
SESSION_SECRET=$session_secret

# 数据加密密钥
ENCRYPTION_KEY=$encryption_key

# ===========================================
# 数据库配置
# ===========================================
# PostgreSQL配置
DATABASE_URL=postgresql://yaosen_user:$db_password@localhost:5432/yaosen_db
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=yaosen_db
DATABASE_USER=yaosen_user
DATABASE_PASSWORD=$db_password

# Redis配置
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=$redis_password

# ===========================================
# 网站配置
# ===========================================
NEXT_PUBLIC_SITE_URL=https://www.yaosen.com
NEXT_PUBLIC_SITE_NAME=漯河耀森企业管理有限公司

# 联系信息
NEXT_PUBLIC_COMPANY_PHONE=************
NEXT_PUBLIC_COMPANY_EMAIL=<EMAIL>
NEXT_PUBLIC_COMPANY_ADDRESS=河南省漯河市临颍县固厢乡中心社区门面房31号

# ===========================================
# 安全策略配置
# ===========================================
# CORS配置
CORS_ORIGIN=https://www.yaosen.com,https://admin.yaosen.com

# 速率限制
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000

# 会话配置
SESSION_TIMEOUT=3600
SESSION_SECURE=true
SESSION_HTTP_ONLY=true

# ===========================================
# 日志和监控配置
# ===========================================
LOG_LEVEL=info
ENABLE_ACCESS_LOG=true
ENABLE_ERROR_LOG=true

# ===========================================
# 功能开关
# ===========================================
ENABLE_REGISTRATION=false
ENABLE_EMAIL_VERIFICATION=true
ENABLE_SMS_VERIFICATION=false
MAINTENANCE_MODE=false

EOF

    # 设置安全的文件权限
    chmod 600 "$env_file"
    
    log_success "安全环境变量文件已创建: $env_file"
    log_warning "请妥善保管此文件，不要提交到版本控制系统"
}

# 生成SSH密钥对
generate_ssh_keys() {
    log_step "生成SSH密钥对..."
    
    local ssh_dir="$HOME/.ssh"
    local key_file="$ssh_dir/yaosen_deploy_key"
    
    # 创建.ssh目录
    mkdir -p "$ssh_dir"
    chmod 700 "$ssh_dir"
    
    if [ -f "$key_file" ]; then
        log_warning "SSH密钥已存在，跳过生成"
        return
    fi
    
    # 生成ED25519密钥（更安全）
    ssh-keygen -t ed25519 -f "$key_file" -N "" -C "yaosen-deploy-$(date +%Y%m%d)"
    
    # 设置正确的权限
    chmod 600 "$key_file"
    chmod 644 "$key_file.pub"
    
    log_success "SSH密钥对已生成:"
    log_info "私钥: $key_file"
    log_info "公钥: $key_file.pub"
    
    echo ""
    log_info "请将以下公钥添加到服务器的 ~/.ssh/authorized_keys 文件中:"
    echo ""
    cat "$key_file.pub"
    echo ""
}

# 创建SSL证书目录和配置
setup_ssl() {
    log_step "设置SSL证书目录..."
    
    local ssl_dir="ssl"
    mkdir -p "$ssl_dir"
    chmod 700 "$ssl_dir"
    
    # 创建自签名证书（仅用于开发）
    if [ ! -f "$ssl_dir/cert.pem" ]; then
        log_info "生成自签名SSL证书（仅用于开发环境）..."
        
        openssl req -x509 -newkey rsa:4096 -keyout "$ssl_dir/key.pem" -out "$ssl_dir/cert.pem" -days 365 -nodes \
            -subj "/C=CN/ST=Henan/L=Luohe/O=Yaosen Enterprise Management Co Ltd/CN=localhost"
        
        chmod 600 "$ssl_dir/key.pem"
        chmod 644 "$ssl_dir/cert.pem"
        
        log_success "自签名SSL证书已生成"
        log_warning "生产环境请使用正式的SSL证书"
    else
        log_info "SSL证书已存在"
    fi
}

# 创建安全配置文件
create_security_config() {
    log_step "创建安全配置文件..."
    
    cat > "security-config.json" << EOF
{
  "security": {
    "passwordPolicy": {
      "minLength": 12,
      "requireUppercase": true,
      "requireLowercase": true,
      "requireNumbers": true,
      "requireSpecialChars": true,
      "maxAge": 90
    },
    "sessionPolicy": {
      "timeout": 3600,
      "secure": true,
      "httpOnly": true,
      "sameSite": "strict"
    },
    "rateLimiting": {
      "enabled": true,
      "maxRequests": 100,
      "windowMs": 900000
    },
    "cors": {
      "enabled": true,
      "allowedOrigins": [
        "https://www.yaosen.com",
        "https://admin.yaosen.com"
      ]
    },
    "headers": {
      "hsts": "max-age=31536000; includeSubDomains",
      "xFrameOptions": "DENY",
      "xContentTypeOptions": "nosniff",
      "xXSSProtection": "1; mode=block",
      "referrerPolicy": "strict-origin-when-cross-origin"
    }
  },
  "monitoring": {
    "enableAccessLog": true,
    "enableErrorLog": true,
    "enableSecurityLog": true,
    "logLevel": "info"
  }
}
EOF

    chmod 644 "security-config.json"
    log_success "安全配置文件已创建: security-config.json"
}

# 更新.gitignore
update_gitignore() {
    log_step "更新.gitignore文件..."
    
    local gitignore_entries=(
        "# 环境变量文件"
        ".env"
        ".env.local"
        ".env.production"
        ".env.development"
        ""
        "# SSL证书和密钥"
        "ssl/*.key"
        "ssl/*.pem"
        "ssl/*.crt"
        ""
        "# SSH密钥"
        "*.key"
        "*.pub"
        ""
        "# 安全相关"
        "secrets/"
        "credentials/"
        "*.backup"
    )
    
    for entry in "${gitignore_entries[@]}"; do
        if ! grep -q "$entry" .gitignore 2>/dev/null; then
            echo "$entry" >> .gitignore
        fi
    done
    
    log_success ".gitignore已更新"
}

# 显示安全提醒
show_security_reminders() {
    echo ""
    log_success "安全初始化完成！"
    echo ""
    echo -e "${CYAN}重要安全提醒:${NC}"
    echo "1. 🔐 所有密码和密钥已生成，请妥善保管"
    echo "2. 🚫 不要将.env文件提交到版本控制系统"
    echo "3. 🔄 定期更换密码和密钥（建议每3个月）"
    echo "4. 🛡️ 生产环境请使用正式的SSL证书"
    echo "5. 📊 定期运行安全检查脚本"
    echo "6. 🔍 监控系统日志，及时发现异常"
    echo ""
    echo -e "${YELLOW}下一步操作:${NC}"
    echo "1. 将SSH公钥添加到服务器"
    echo "2. 配置服务器防火墙"
    echo "3. 运行 ./security-check.sh 进行安全检查"
    echo "4. 部署到生产环境前进行安全测试"
    echo ""
}

# 主函数
main() {
    show_header
    
    # 检查是否为root用户
    if [ "$EUID" -eq 0 ]; then
        log_error "请不要以root用户运行此脚本"
        exit 1
    fi
    
    # 检查必要的工具
    local required_tools=("openssl" "ssh-keygen")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "缺少必要工具: $tool"
            exit 1
        fi
    done
    
    create_secure_env
    generate_ssh_keys
    setup_ssl
    create_security_config
    update_gitignore
    show_security_reminders
}

# 运行主函数
main "$@"

#!/bin/bash

# 耀森网站安全检查脚本
# 用于检查项目的安全配置和潜在风险

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((PASSED_CHECKS++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
    ((WARNING_CHECKS++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((FAILED_CHECKS++))
}

log_check() {
    echo -e "${CYAN}[CHECK]${NC} $1"
    ((TOTAL_CHECKS++))
}

# 显示标题
show_header() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "         耀森网站安全检查工具"
    echo "=================================================="
    echo -e "${NC}"
    echo "正在检查项目安全配置..."
    echo ""
}

# 检查文件权限
check_file_permissions() {
    log_check "检查文件权限..."
    
    # 检查敏感文件权限
    local sensitive_files=(".env" ".env.local" "ssl/key.pem" "ssl/cert.pem")
    
    for file in "${sensitive_files[@]}"; do
        if [ -f "$file" ]; then
            local perms=$(stat -c "%a" "$file" 2>/dev/null || stat -f "%A" "$file" 2>/dev/null)
            if [ "$perms" -gt 600 ]; then
                log_warning "文件 $file 权限过于宽松 ($perms)，建议设置为600"
            else
                log_success "文件 $file 权限配置正确 ($perms)"
            fi
        fi
    done
    
    # 检查脚本文件权限
    local script_files=("deploy.sh" "sync-to-server.sh" "quick-deploy.sh")
    
    for script in "${script_files[@]}"; do
        if [ -f "$script" ]; then
            if [ -x "$script" ]; then
                log_success "脚本 $script 具有执行权限"
            else
                log_warning "脚本 $script 缺少执行权限"
            fi
        fi
    done
}

# 检查敏感信息泄露
check_sensitive_info() {
    log_check "检查敏感信息泄露..."
    
    # 检查是否有硬编码的密码
    local password_patterns=("password.*=.*['\"][^'\"]{1,}" "pwd.*=.*['\"][^'\"]{1,}" "secret.*=.*['\"][^'\"]{1,}")
    
    for pattern in "${password_patterns[@]}"; do
        local matches=$(grep -r -i "$pattern" --include="*.js" --include="*.ts" --include="*.tsx" --include="*.json" --include="*.md" . 2>/dev/null || true)
        if [ -n "$matches" ]; then
            log_warning "发现可能的硬编码密码:"
            echo "$matches" | head -5
        fi
    done
    
    # 检查是否还有远程开发相关配置
    if [ -f "REMOTE-DEV.md" ]; then
        log_warning "发现远程开发文档，建议移除以提高安全性"
    else
        log_success "已移除远程开发模块，安全性提升"
    fi
    
    # 检查API密钥模式
    local api_patterns=("api[_-]?key" "access[_-]?token" "secret[_-]?key")
    
    for pattern in "${api_patterns[@]}"; do
        local matches=$(grep -r -i "$pattern.*=.*['\"][a-zA-Z0-9]{20,}" --include="*.js" --include="*.ts" --include="*.tsx" . 2>/dev/null || true)
        if [ -n "$matches" ]; then
            log_warning "发现可能的API密钥泄露:"
            echo "$matches" | head -3
        fi
    done
}

# 检查Docker安全配置
check_docker_security() {
    log_check "检查Docker安全配置..."
    
    # 检查Dockerfile中的用户配置
    if grep -q "USER nextjs" Dockerfile 2>/dev/null; then
        log_success "Docker容器使用非root用户运行"
    else
        log_error "Docker容器可能以root用户运行，存在安全风险"
    fi
    
    # 检查是否暴露了不必要的端口
    local exposed_ports=$(grep -o "EXPOSE [0-9]*" Dockerfile 2>/dev/null || true)
    if [ -n "$exposed_ports" ]; then
        log_info "Docker暴露的端口: $exposed_ports"
    fi
    
    # 检查docker-compose安全配置
    if [ -f "docker-compose.yml" ]; then
        # 检查是否使用了privileged模式
        if grep -q "privileged.*true" docker-compose.yml 2>/dev/null; then
            log_error "Docker容器使用特权模式，存在安全风险"
        else
            log_success "Docker容器未使用特权模式"
        fi
        
        # 检查是否挂载了敏感目录
        local sensitive_mounts=("/etc" "/var" "/usr" "/bin" "/sbin")
        for mount in "${sensitive_mounts[@]}"; do
            if grep -q "$mount:" docker-compose.yml 2>/dev/null; then
                log_warning "检测到敏感目录挂载: $mount"
            fi
        done
    fi
}

# 检查网络安全配置
check_network_security() {
    log_check "检查网络安全配置..."
    
    # 检查nginx配置
    if [ -f "nginx.conf" ]; then
        # 检查安全头
        local security_headers=("X-Frame-Options" "X-XSS-Protection" "X-Content-Type-Options")
        
        for header in "${security_headers[@]}"; do
            if grep -q "$header" nginx.conf 2>/dev/null; then
                log_success "Nginx配置包含安全头: $header"
            else
                log_warning "Nginx配置缺少安全头: $header"
            fi
        done
        
        # 检查HTTPS配置
        if grep -q "listen 443 ssl" nginx.conf 2>/dev/null; then
            log_success "Nginx配置了HTTPS"
        else
            log_warning "Nginx未配置HTTPS，建议启用SSL"
        fi
        
        # 检查server_tokens
        if grep -q "server_tokens off" nginx.conf 2>/dev/null; then
            log_success "Nginx隐藏了版本信息"
        else
            log_warning "Nginx未隐藏版本信息，建议添加 server_tokens off"
        fi
    fi
}

# 检查依赖安全
check_dependencies() {
    log_check "检查依赖安全..."
    
    # 检查package.json中的依赖
    if [ -f "package.json" ]; then
        # 检查是否有已知的不安全包
        local unsafe_packages=("lodash" "moment" "request")
        
        for package in "${unsafe_packages[@]}"; do
            if grep -q "\"$package\"" package.json 2>/dev/null; then
                log_warning "检测到可能不安全的包: $package，建议检查版本"
            fi
        done
        
        # 检查是否有npm audit
        if command -v npm &> /dev/null; then
            log_info "运行 npm audit 检查依赖漏洞..."
            npm audit --audit-level=moderate 2>/dev/null || log_warning "npm audit 发现安全问题，请运行 npm audit fix"
        fi
    fi
}

# 检查环境变量配置
check_env_config() {
    log_check "检查环境变量配置..."
    
    # 检查是否有.env.example文件
    if [ -f ".env.example" ]; then
        log_success "存在环境变量模板文件"
    else
        log_warning "缺少环境变量模板文件 .env.example"
    fi
    
    # 检查是否有.env文件被意外提交
    if [ -f ".env" ] && [ -d ".git" ]; then
        if git ls-files --error-unmatch .env 2>/dev/null; then
            log_error ".env文件被提交到版本控制，存在敏感信息泄露风险"
        else
            log_success ".env文件未被提交到版本控制"
        fi
    fi
    
    # 检查.gitignore配置
    if [ -f ".gitignore" ]; then
        local env_patterns=(".env" ".env.local" ".env.production")
        
        for pattern in "${env_patterns[@]}"; do
            if grep -q "$pattern" .gitignore 2>/dev/null; then
                log_success ".gitignore包含环境变量文件: $pattern"
            else
                log_warning ".gitignore缺少环境变量文件: $pattern"
            fi
        done
    else
        log_warning "缺少.gitignore文件"
    fi
}

# 检查SSL证书
check_ssl_certificates() {
    log_check "检查SSL证书..."
    
    if [ -d "ssl" ]; then
        if [ -f "ssl/cert.pem" ] && [ -f "ssl/key.pem" ]; then
            log_success "SSL证书文件存在"
            
            # 检查证书有效期
            if command -v openssl &> /dev/null; then
                local expiry=$(openssl x509 -in ssl/cert.pem -noout -enddate 2>/dev/null | cut -d= -f2)
                if [ -n "$expiry" ]; then
                    log_info "SSL证书到期时间: $expiry"
                fi
            fi
        else
            log_warning "SSL目录存在但缺少证书文件"
        fi
    else
        log_warning "未找到SSL证书目录"
    fi
}

# 检查备份配置
check_backup_config() {
    log_check "检查备份配置..."
    
    # 检查是否有备份脚本
    local backup_scripts=("backup.sh" "scripts/backup.sh")
    local backup_found=false
    
    for script in "${backup_scripts[@]}"; do
        if [ -f "$script" ]; then
            log_success "找到备份脚本: $script"
            backup_found=true
        fi
    done
    
    if [ "$backup_found" = false ]; then
        log_warning "未找到备份脚本，建议创建自动备份机制"
    fi
    
    # 检查部署脚本中的备份功能
    if grep -q "backup" deploy.sh 2>/dev/null; then
        log_success "部署脚本包含备份功能"
    else
        log_warning "部署脚本缺少备份功能"
    fi
}

# 生成安全报告
generate_report() {
    echo ""
    echo -e "${BLUE}"
    echo "=================================================="
    echo "              安全检查报告"
    echo "=================================================="
    echo -e "${NC}"
    
    echo "总检查项: $TOTAL_CHECKS"
    echo -e "${GREEN}通过: $PASSED_CHECKS${NC}"
    echo -e "${YELLOW}警告: $WARNING_CHECKS${NC}"
    echo -e "${RED}失败: $FAILED_CHECKS${NC}"
    
    echo ""
    
    # 计算安全评分
    local score=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    
    if [ $score -ge 90 ]; then
        echo -e "${GREEN}安全评分: $score/100 (优秀)${NC}"
    elif [ $score -ge 70 ]; then
        echo -e "${YELLOW}安全评分: $score/100 (良好)${NC}"
    elif [ $score -ge 50 ]; then
        echo -e "${YELLOW}安全评分: $score/100 (一般)${NC}"
    else
        echo -e "${RED}安全评分: $score/100 (需要改进)${NC}"
    fi
    
    echo ""
    
    # 提供改进建议
    if [ $FAILED_CHECKS -gt 0 ] || [ $WARNING_CHECKS -gt 0 ]; then
        echo -e "${CYAN}改进建议:${NC}"
        echo "1. 查看上述警告和错误信息"
        echo "2. 参考 SECURITY-IMPROVEMENTS.md 文件"
        echo "3. 定期运行此安全检查脚本"
        echo "4. 保持系统和依赖更新"
    fi
}

# 主函数
main() {
    show_header
    
    check_file_permissions
    check_sensitive_info
    check_docker_security
    check_network_security
    check_dependencies
    check_env_config
    check_ssl_certificates
    check_backup_config
    
    generate_report
}

# 运行主函数
main "$@"

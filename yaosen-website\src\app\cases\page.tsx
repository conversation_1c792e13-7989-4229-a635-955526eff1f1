import { Building2, TrendingUp, Users, Award } from 'lucide-react';

export default function Cases() {
  const cases = [
    {
      company: "河南某制造企业",
      industry: "制造业",
      service: "企业管理咨询",
      challenge: "企业规模快速扩张，管理体系跟不上发展需要，出现效率低下、成本上升等问题。",
      solution: "重新设计组织架构，优化业务流程，建立标准化管理制度，实施绩效管理体系。",
      result: "管理效率提升40%，运营成本降低25%，员工满意度显著提高。",
      duration: "6个月",
      icon: <Building2 className="text-blue-600" size={48} />
    },
    {
      company: "漯河某科技公司",
      industry: "科技服务",
      service: "人力资源管理",
      challenge: "人才流失率高，招聘困难，员工发展通道不清晰，团队凝聚力不足。",
      solution: "建立完善的人才招聘体系，设计职业发展路径，实施员工培训计划，优化薪酬福利结构。",
      result: "人才流失率降低60%，员工满意度提升50%，团队效能明显改善。",
      duration: "4个月",
      icon: <Users className="text-green-600" size={48} />
    },
    {
      company: "某商贸集团",
      industry: "商贸流通",
      service: "财务管理服务",
      challenge: "财务管理混乱，成本控制不力，资金使用效率低，缺乏有效的财务分析。",
      solution: "建立规范的财务管理制度，实施预算管理体系，优化成本控制流程，建立财务分析报告体系。",
      result: "财务管理规范化，成本控制效果显著，资金使用效率提升35%。",
      duration: "5个月",
      icon: <TrendingUp className="text-purple-600" size={48} />
    },
    {
      company: "某餐饮连锁企业",
      industry: "餐饮服务",
      service: "运营优化",
      challenge: "门店管理标准不统一，服务质量参差不齐，运营效率有待提升。",
      solution: "制定标准化运营流程，建立质量管理体系，实施员工培训计划，优化供应链管理。",
      result: "服务质量标准化，客户满意度提升45%，运营效率显著改善。",
      duration: "3个月",
      icon: <Award className="text-orange-600" size={48} />
    }
  ];

  const stats = [
    { number: "100+", label: "服务企业", description: "累计为超过100家企业提供专业服务" },
    { number: "95%", label: "客户满意度", description: "客户对我们服务的满意度达到95%以上" },
    { number: "80%", label: "效率提升", description: "平均帮助企业提升管理效率80%以上" },
    { number: "4年", label: "服务经验", description: "在企业管理服务领域深耕4年" }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">成功案例</h1>
            <p className="text-xl lg:text-2xl text-blue-100">
              真实的案例，显著的成效，见证我们的专业实力
            </p>
          </div>
        </div>
      </section>

      {/* 数据统计 */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-800 mb-4">
              服务成果
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              用数据说话，用成果证明我们的专业能力和服务价值
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center p-6 bg-white rounded-lg shadow-lg">
                <div className="text-4xl lg:text-5xl font-bold text-blue-600 mb-2">
                  {stat.number}
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">
                  {stat.label}
                </h3>
                <p className="text-gray-600 text-sm">
                  {stat.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 案例展示 */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-800 mb-4">
              典型案例
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              以下是我们为客户提供服务的典型案例，展示了我们的专业能力和服务效果
            </p>
          </div>
          
          <div className="space-y-12">
            {cases.map((caseItem, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden">
                <div className="p-8">
                  <div className="flex flex-col lg:flex-row lg:items-start lg:space-x-8">
                    <div className="flex-shrink-0 mb-6 lg:mb-0">
                      {caseItem.icon}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4">
                        <div>
                          <h3 className="text-2xl font-bold text-gray-800 mb-2">
                            {caseItem.company}
                          </h3>
                          <div className="flex flex-wrap gap-4 text-sm">
                            <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                              {caseItem.industry}
                            </span>
                            <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full">
                              {caseItem.service}
                            </span>
                            <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full">
                              项目周期：{caseItem.duration}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div>
                          <h4 className="font-semibold text-gray-800 mb-2">面临挑战</h4>
                          <p className="text-gray-600 text-sm leading-relaxed">
                            {caseItem.challenge}
                          </p>
                        </div>
                        
                        <div>
                          <h4 className="font-semibold text-gray-800 mb-2">解决方案</h4>
                          <p className="text-gray-600 text-sm leading-relaxed">
                            {caseItem.solution}
                          </p>
                        </div>
                        
                        <div>
                          <h4 className="font-semibold text-gray-800 mb-2">实施效果</h4>
                          <p className="text-green-600 text-sm leading-relaxed font-medium">
                            {caseItem.result}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 客户评价 */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-800 mb-4">
              客户评价
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              听听我们的客户怎么说
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <div className="mb-4">
                <div className="flex text-yellow-400 mb-2">
                  ★★★★★
                </div>
                <p className="text-gray-600 italic">
                  &ldquo;耀森团队非常专业，帮助我们解决了很多管理难题，效果显著。&rdquo;
                </p>
              </div>
              <div className="border-t pt-4">
                <p className="font-semibold text-gray-800">张总经理</p>
                <p className="text-sm text-gray-500">某制造企业</p>
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <div className="mb-4">
                <div className="flex text-yellow-400 mb-2">
                  ★★★★★
                </div>
                <p className="text-gray-600 italic">
                  &ldquo;服务态度很好，方案很实用，实施效果超出预期。&rdquo;
                </p>
              </div>
              <div className="border-t pt-4">
                <p className="font-semibold text-gray-800">李总</p>
                <p className="text-sm text-gray-500">某科技公司</p>
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <div className="mb-4">
                <div className="flex text-yellow-400 mb-2">
                  ★★★★★
                </div>
                <p className="text-gray-600 italic">
                  &ldquo;专业的团队，贴心的服务，值得信赖的合作伙伴。&rdquo;
                </p>
              </div>
              <div className="border-t pt-4">
                <p className="font-semibold text-gray-800">王董事长</p>
                <p className="text-sm text-gray-500">某商贸集团</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

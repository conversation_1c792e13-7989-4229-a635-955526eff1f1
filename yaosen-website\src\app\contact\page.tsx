'use client';

import { useState } from 'react';
import { Phone, Mail, MapPin, Clock, Send, ExternalLink } from 'lucide-react';

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    company: '',
    phone: '',
    email: '',
    service: '',
    message: ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // 这里可以添加表单提交逻辑
    console.log('表单数据:', formData);
    alert('感谢您的咨询，我们会尽快与您联系！');
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">联系我们</h1>
            <p className="text-xl lg:text-2xl text-blue-100">
              专业的综合服务团队，随时为您提供全方位的咨询和支持
            </p>
          </div>
        </div>
      </section>

      {/* 联系信息和表单 */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* 联系信息 */}
            <div>
              <h2 className="text-3xl font-bold text-gray-800 mb-8">联系信息</h2>
              
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <MapPin className="text-blue-600" size={24} />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-1">公司地址</h3>
                    <p className="text-gray-600">安徽省合肥市蜀山区蜀山经济开发区</p>
                    <p className="text-gray-600">中国(安徽)自由贸易试验区合肥片区</p>
                    <p className="text-gray-600">花峰路1201号跨境电商产业园三期3幢G区8层1326号</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <Phone className="text-green-600" size={24} />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-1">联系电话</h3>
                    <p className="text-gray-600">************</p>
                    <p className="text-gray-600">0395-123-4567</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Mail className="text-purple-600" size={24} />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-1">电子邮箱</h3>
                    <p className="text-gray-600"><EMAIL></p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <Clock className="text-orange-600" size={24} />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-1">工作时间</h3>
                    <p className="text-gray-600">周一至周五：9:00 - 18:00</p>
                    <p className="text-gray-600">周六：9:00 - 17:00</p>
                  </div>
                </div>
              </div>
              
              {/* 位置地图 */}
              <div className="mt-8">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">位置地图</h3>
                <div className="w-full h-64 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg overflow-hidden relative">
                  {/* 地图位置显示 */}
                  <div className="w-full h-full flex flex-col items-center justify-center text-center p-6">
                    <div className="bg-white rounded-full p-4 mb-4 shadow-lg">
                      <MapPin className="text-blue-600" size={48} />
                    </div>
                    <h4 className="text-xl font-semibold text-gray-800 mb-2">公司位置</h4>
                    <p className="text-gray-600 mb-2">安徽省合肥市蜀山区蜀山经济开发区</p>
                    <p className="text-gray-600 mb-2">中国(安徽)自由贸易试验区合肥片区</p>
                    <p className="text-gray-600 mb-6">花峰路1201号跨境电商产业园三期3幢G区8层1326号</p>

                    {/* 地图导航按钮 */}
                    <div className="flex flex-col sm:flex-row gap-3">
                      <a
                        href="https://map.baidu.com/search/安徽省合肥市蜀山区花峰路1201号跨境电商产业园三期3幢G区8层1326号"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm"
                      >
                        <ExternalLink size={16} className="mr-2" />
                        百度地图导航
                      </a>
                      <a
                        href="https://uri.amap.com/search?query=安徽省合肥市蜀山区花峰路1201号跨境电商产业园三期3幢G区8层1326号"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 text-sm"
                      >
                        <ExternalLink size={16} className="mr-2" />
                        高德地图导航
                      </a>
                    </div>
                  </div>

                  {/* 装饰性地图图标 */}
                  <div className="absolute top-4 left-4 opacity-20">
                    <div className="w-8 h-8 bg-blue-600 rounded-full"></div>
                  </div>
                  <div className="absolute bottom-4 right-4 opacity-20">
                    <div className="w-6 h-6 bg-green-600 rounded-full"></div>
                  </div>
                  <div className="absolute top-1/2 right-8 opacity-20">
                    <div className="w-4 h-4 bg-purple-600 rounded-full"></div>
                  </div>
                </div>

                {/* 地址信息卡片 */}
                <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-100">
                  <div className="flex items-start space-x-3">
                    <div className="bg-blue-600 rounded-full p-2">
                      <MapPin className="text-white" size={16} />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-800 mb-1">详细地址</h4>
                      <p className="text-gray-600 mb-2">安徽省合肥市蜀山区蜀山经济开发区中国(安徽)自由贸易试验区合肥片区花峰路1201号跨境电商产业园三期3幢G区8层1326号</p>
                      <div className="flex flex-col sm:flex-row gap-2 text-sm">
                        <span className="inline-flex items-center text-gray-500">
                          📍 导航提示：搜索"花峰路1201号跨境电商产业园"
                        </span>
                        <span className="inline-flex items-center text-gray-500">
                          🚗 建议使用导航软件精确定位
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 联系表单 */}
            <div>
              <h2 className="text-3xl font-bold text-gray-800 mb-8">在线咨询</h2>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                      姓名 *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="请输入您的姓名"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">
                      公司名称
                    </label>
                    <input
                      type="text"
                      id="company"
                      name="company"
                      value={formData.company}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="请输入公司名称"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                      联系电话 *
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      required
                      value={formData.phone}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="请输入联系电话"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                      电子邮箱
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="请输入电子邮箱"
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-2">
                    咨询服务
                  </label>
                  <select
                    id="service"
                    name="service"
                    value={formData.service}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">请选择咨询服务</option>
                    <option value="management">企业管理咨询</option>
                    <option value="technology">技术服务</option>
                    <option value="data">数据处理服务</option>
                    <option value="consulting">专业咨询服务</option>
                    <option value="business">商务代理服务</option>
                    <option value="engineering">工程建设</option>
                    <option value="sales">产品销售</option>
                    <option value="digital">数字内容制作</option>
                    <option value="other">其他服务</option>
                  </select>
                </div>
                
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                    详细需求 *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    required
                    rows={5}
                    value={formData.message}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请详细描述您的需求和问题"
                  ></textarea>
                </div>
                
                <button
                  type="submit"
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center"
                >
                  <Send className="mr-2" size={20} />
                  提交咨询
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

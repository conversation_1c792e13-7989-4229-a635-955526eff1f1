import type { Metadata } from "next";
import { Geist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "漯河耀森企业管理有限公司 - 专业企业管理服务",
  description: "漯河耀森企业管理有限公司是一家专业的企业管理服务公司，由安徽追逐科技有限公司提供技术支持。提供企业管理咨询、技术服务、数据处理、工程建设、产品销售等全方位服务。技术开发地址：安徽省合肥市蜀山区蜀山经济开发区中国(安徽)自由贸易试验区合肥片区花峰路1201号跨境电商产业园三期3幢G区8层1326号",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Navigation />
        <main>{children}</main>
        <Footer />
      </body>
    </html>
  );
}

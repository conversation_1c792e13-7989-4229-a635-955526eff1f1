import Link from 'next/link';
import { ArrowRight, Users, Target, Award, TrendingUp, Building2, Cpu, Leaf, ShoppingCart, MapPin, Calendar } from 'lucide-react';

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 text-white py-20 lg:py-32">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
              综合企业服务专家
              <span className="block text-blue-300">全方位助力企业发展</span>
            </h1>
            <p className="text-xl lg:text-2xl mb-8 text-blue-100 leading-relaxed">
              安徽追逐科技有限公司提供企业管理、技术服务、工程建设、产品销售等全方位综合服务
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200 flex items-center justify-center"
              >
                立即咨询
                <ArrowRight className="ml-2" size={20} />
              </Link>
              <Link
                href="/services"
                className="border-2 border-white text-white hover:bg-white hover:text-blue-900 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200"
              >
                了解服务
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 核心优势 */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-800 mb-4">
              为什么选择耀森
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              我们拥有专业的团队、丰富的经验和完善的服务体系，为企业提供全方位的综合服务解决方案
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center p-6 bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-200">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Building2 className="text-blue-600" size={32} />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">管理咨询</h3>
              <p className="text-gray-600">企业管理、咨询策划、市场营销等专业服务</p>
            </div>

            <div className="text-center p-6 bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-200">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Cpu className="text-green-600" size={32} />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">技术服务</h3>
              <p className="text-gray-600">数字技术、信息技术咨询、数据处理等服务</p>
            </div>

            <div className="text-center p-6 bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-200">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Leaf className="text-yellow-600" size={32} />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">工程建设</h3>
              <p className="text-gray-600">园林绿化工程、土石方工程施工服务</p>
            </div>

            <div className="text-center p-6 bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-200">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <ShoppingCart className="text-purple-600" size={32} />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">产品销售</h3>
              <p className="text-gray-600">电力电子元器件、金属结构、电线电缆销售</p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务概览 */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-800 mb-4">
              核心服务
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              提供全方位的综合服务，涵盖企业管理、技术服务、工程建设、产品销售等各个领域
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-200">
              <h3 className="text-2xl font-semibold text-gray-800 mb-4">企业管理咨询</h3>
              <p className="text-gray-600 mb-6">
                提供企业管理、咨询策划、市场营销策划、企业形象策划等全方位管理咨询服务。
              </p>
              <Link
                href="/services"
                className="text-blue-600 hover:text-blue-700 font-semibold flex items-center"
              >
                了解更多
                <ArrowRight className="ml-2" size={16} />
              </Link>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-200">
              <h3 className="text-2xl font-semibold text-gray-800 mb-4">技术服务</h3>
              <p className="text-gray-600 mb-6">
                提供数字技术服务、信息技术咨询、数据处理存储、技术开发转让等专业技术服务。
              </p>
              <Link
                href="/services"
                className="text-blue-600 hover:text-blue-700 font-semibold flex items-center"
              >
                了解更多
                <ArrowRight className="ml-2" size={16} />
              </Link>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-200">
              <h3 className="text-2xl font-semibold text-gray-800 mb-4">工程与销售</h3>
              <p className="text-gray-600 mb-6">
                提供园林绿化工程、土石方工程施工，以及电力电子元器件、电线电缆等产品销售。
              </p>
              <Link
                href="/services"
                className="text-blue-600 hover:text-blue-700 font-semibold flex items-center"
              >
                了解更多
                <ArrowRight className="ml-2" size={16} />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 公司信息 */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-800 mb-4">
              公司信息
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              安徽追逐科技有限公司基本信息
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div className="bg-white p-8 rounded-lg shadow-lg text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Calendar className="text-blue-600" size={32} />
              </div>
              <h3 className="text-2xl font-semibold text-gray-800 mb-2">成立时间</h3>
              <p className="text-gray-600 text-lg">2023年07月27日</p>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-lg text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin className="text-green-600" size={32} />
              </div>
              <h3 className="text-2xl font-semibold text-gray-800 mb-2">公司地址</h3>
              <p className="text-gray-600">安徽省合肥市蜀山区蜀山经济开发区</p>
              <p className="text-gray-600">中国(安徽)自由贸易试验区合肥片区</p>
              <p className="text-gray-600">花峰路1201号跨境电商产业园三期3幢G区8层1326号</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-6">
            准备开始您的企业发展新征程？
          </h2>
          <p className="text-xl mb-8 text-blue-100 max-w-2xl mx-auto">
            联系我们的专业团队，获取免费的咨询服务和全方位的综合解决方案
          </p>
          <Link
            href="/contact"
            className="bg-white text-blue-900 hover:bg-gray-100 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200 inline-flex items-center"
          >
            免费咨询
            <ArrowRight className="ml-2" size={20} />
          </Link>
        </div>
      </section>
    </div>
  );
}

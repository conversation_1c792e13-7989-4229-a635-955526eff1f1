import {
  Briefcase,
  Users,
  Calculator,
  GraduationCap,
  Target,
  BarChart3,
  CheckCircle,
  ArrowRight,
  Cpu,
  Database,
  Leaf,
  ShoppingCart,
  FileText,
  Globe,
  Palette,
  Building2
} from 'lucide-react';
import Link from 'next/link';

export default function Services() {
  const services = [
    {
      icon: <Briefcase className="text-blue-600" size={48} />,
      title: "企业管理咨询",
      description: "为企业提供全方位的管理咨询服务，包括企业管理、咨询策划、市场营销策划等",
      features: [
        "企业管理咨询",
        "咨询策划服务",
        "市场营销策划",
        "企业形象策划",
        "个人商务服务"
      ]
    },
    {
      icon: <Cpu className="text-green-600" size={48} />,
      title: "技术服务",
      description: "提供数字技术、信息技术等现代化技术服务，助力企业数字化转型",
      features: [
        "数字技术服务",
        "信息技术咨询服务",
        "技术开发与转让",
        "技术咨询与推广",
        "技术交流服务"
      ]
    },
    {
      icon: <Database className="text-purple-600" size={48} />,
      title: "数据处理服务",
      description: "专业的数据处理和存储支持服务，为企业提供数据管理解决方案",
      features: [
        "数据处理服务",
        "数据存储支持",
        "数字内容制作",
        "信息系统维护",
        "数据安全保障"
      ]
    },
    {
      icon: <FileText className="text-orange-600" size={48} />,
      title: "专业咨询服务",
      description: "提供多领域的专业咨询服务，为企业决策提供专业支持",
      features: [
        "社会经济咨询",
        "健康咨询服务",
        "环保咨询服务",
        "票据信息咨询",
        "信息咨询服务"
      ]
    },
    {
      icon: <Building2 className="text-red-600" size={48} />,
      title: "商务代理服务",
      description: "提供全方位的商务代理服务，简化企业办事流程",
      features: [
        "市场主体登记注册代理",
        "国内贸易代理",
        "市场调查服务",
        "会议及展览服务",
        "广告设计服务"
      ]
    },
    {
      icon: <Leaf className="text-green-700" size={48} />,
      title: "工程建设",
      description: "专业的工程建设服务，包括园林绿化和土石方工程",
      features: [
        "园林绿化工程施工",
        "土石方工程施工",
        "工程项目管理",
        "工程质量监控",
        "工程技术咨询"
      ]
    },
    {
      icon: <ShoppingCart className="text-indigo-600" size={48} />,
      title: "产品销售",
      description: "提供多种工业产品销售服务，满足企业采购需求",
      features: [
        "电力电子元器件销售",
        "金属结构销售",
        "电线电缆经营",
        "产品质量保证",
        "售后服务支持"
      ]
    },
    {
      icon: <Palette className="text-pink-600" size={48} />,
      title: "数字内容制作",
      description: "专业的数字内容制作服务，助力企业品牌建设和宣传推广",
      features: [
        "数字内容制作",
        "企业宣传材料",
        "多媒体内容设计",
        "品牌视觉设计",
        "数字化展示方案"
      ]
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">服务项目</h1>
            <p className="text-xl lg:text-2xl text-blue-100">
              提供全方位的综合服务，涵盖管理咨询、技术服务、工程建设、产品销售等领域
            </p>
          </div>
        </div>
      </section>

      {/* 服务概览 */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-800 mb-4">
              专业服务体系
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              我们提供涵盖多个领域的专业服务，从企业管理到技术服务，从工程建设到产品销售，全方位满足企业需求
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {services.map((service, index) => (
              <div key={index} className="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-200">
                <div className="mb-6">
                  {service.icon}
                </div>
                <h3 className="text-2xl font-semibold text-gray-800 mb-4">
                  {service.title}
                </h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {service.description}
                </p>
                <ul className="space-y-2 mb-6">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-gray-600">
                      <CheckCircle className="text-green-500 mr-2" size={16} />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Link
                  href="/contact"
                  className="text-blue-600 hover:text-blue-700 font-semibold flex items-center"
                >
                  了解详情
                  <ArrowRight className="ml-2" size={16} />
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 服务流程 */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-800 mb-4">
              服务流程
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              标准化的服务流程，确保每个项目都能得到专业、高效的执行
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                1
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">需求分析</h3>
              <p className="text-gray-600">深入了解企业现状和需求，制定初步方案</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                2
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">方案设计</h3>
              <p className="text-gray-600">根据需求分析结果，设计定制化解决方案</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                3
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">实施执行</h3>
              <p className="text-gray-600">专业团队负责方案实施，确保执行效果</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                4
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">跟踪优化</h3>
              <p className="text-gray-600">持续跟踪效果，优化方案，确保长期成功</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-6">
            需要专业的综合服务？
          </h2>
          <p className="text-xl mb-8 text-blue-100 max-w-2xl mx-auto">
            联系我们的专业团队，获取免费的咨询服务和全方位的解决方案
          </p>
          <Link
            href="/contact"
            className="bg-white text-blue-900 hover:bg-gray-100 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200 inline-flex items-center"
          >
            立即咨询
            <ArrowRight className="ml-2" size={20} />
          </Link>
        </div>
      </section>
    </div>
  );
}

import Link from 'next/link';
import { Phone, Mail, MapPin, Clock } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* 公司信息 */}
          <div>
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">追</span>
              </div>
              <div>
                <h3 className="text-lg font-bold">追逐科技</h3>
                <p className="text-sm text-gray-400">安徽追逐科技有限公司</p>
              </div>
            </div>
            <p className="text-gray-400 text-sm leading-relaxed">
              综合性企业服务提供商，致力于为企业提供全方位的管理咨询、技术服务、工程建设等综合解决方案。
            </p>
          </div>

          {/* 快速链接 */}
          <div>
            <h4 className="text-lg font-semibold mb-6">快速链接</h4>
            <ul className="space-y-3">
              <li>
                <Link href="/about" className="text-gray-400 hover:text-white transition-colors duration-200">
                  关于我们
                </Link>
              </li>
              <li>
                <Link href="/services" className="text-gray-400 hover:text-white transition-colors duration-200">
                  服务项目
                </Link>
              </li>
              <li>
                <Link href="/cases" className="text-gray-400 hover:text-white transition-colors duration-200">
                  成功案例
                </Link>
              </li>

              <li>
                <Link href="/contact" className="text-gray-400 hover:text-white transition-colors duration-200">
                  联系我们
                </Link>
              </li>
            </ul>
          </div>

          {/* 服务项目 */}
          <div>
            <h4 className="text-lg font-semibold mb-6">核心服务</h4>
            <ul className="space-y-3">
              <li className="text-gray-400">企业管理咨询</li>
              <li className="text-gray-400">技术服务</li>
              <li className="text-gray-400">数据处理服务</li>
              <li className="text-gray-400">工程建设</li>
              <li className="text-gray-400">产品销售</li>
            </ul>
          </div>

          {/* 联系信息 */}
          <div>
            <h4 className="text-lg font-semibold mb-6">联系我们</h4>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <MapPin size={18} className="text-blue-400 mt-1" />
                <div>
                  <p className="text-gray-400 text-sm">安徽省合肥市蜀山区蜀山经济开发区</p>
                  <p className="text-gray-400 text-sm">中国(安徽)自由贸易试验区合肥片区</p>
                  <p className="text-gray-400 text-sm">花峰路1201号跨境电商产业园三期3幢G区8层1326号</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Phone size={18} className="text-blue-400" />
                <span className="text-gray-400 text-sm">************</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail size={18} className="text-blue-400" />
                <span className="text-gray-400 text-sm"><EMAIL></span>
              </div>
              <div className="flex items-start space-x-3">
                <Clock size={18} className="text-blue-400 mt-1" />
                <div>
                  <p className="text-gray-400 text-sm">周一至周五</p>
                  <p className="text-gray-400 text-sm">9:00 - 18:00</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 底部版权信息 */}
        <div className="border-t border-gray-800 mt-12 pt-8 pb-4">
          <div className="flex flex-col items-center text-center space-y-6">
            {/* 版权信息主行 - 美化版 */}
            <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg px-6 py-4 border border-gray-800/50">
              <div className="flex flex-col lg:flex-row items-center justify-between gap-4 lg:gap-8 text-gray-400 text-sm">
                {/* 左侧信息组 */}
                <div className="flex flex-col sm:flex-row items-center gap-3 sm:gap-6 order-2 lg:order-1">
                  {/* 备案号 */}
                  <span className="hover:text-blue-400 transition-colors duration-300 cursor-pointer flex items-center gap-2">
                    <span className="w-1 h-1 bg-gray-500 rounded-full"></span>
                    网站备案号：皖ICP备2025084445号-1
                  </span>

                  {/* 分隔符 */}
                  <span className="hidden sm:inline text-gray-600 text-lg">•</span>

                  {/* ICP许可证 */}
                  <span className="hover:text-blue-400 transition-colors duration-300 cursor-pointer flex items-center gap-2">
                    <span className="w-1 h-1 bg-gray-500 rounded-full"></span>
                    ICP许可证编号：皖B2-20250341
                  </span>
                </div>

                {/* 右侧版权信息 - 突出显示 */}
                <div className="order-1 lg:order-2">
                  <span className="font-semibold text-white flex items-center gap-3 bg-gradient-to-r from-blue-600/30 to-purple-600/30 px-4 py-2 rounded-lg border border-blue-500/40 shadow-lg backdrop-blur-sm">
                    <span className="w-2 h-2 bg-blue-400 rounded-full animate-pulse shadow-sm shadow-blue-400/50"></span>
                    <span className="text-gray-100">Copyright © 安徽追逐科技有限公司 版权所有</span>
                    <span className="w-2 h-2 bg-purple-400 rounded-full animate-pulse shadow-sm shadow-purple-400/50"></span>
                  </span>
                </div>
              </div>
            </div>

            {/* 装饰性元素 */}
            <div className="flex items-center justify-center space-x-6">
              <div className="w-16 h-px bg-gradient-to-r from-transparent via-blue-500/30 to-transparent"></div>
              <div className="flex space-x-1">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse"></div>
                <div className="w-1.5 h-1.5 bg-blue-400 rounded-full animate-bounce"></div>
                <div className="w-1.5 h-1.5 bg-blue-300 rounded-full animate-pulse"></div>
              </div>
              <div className="w-16 h-px bg-gradient-to-l from-transparent via-blue-500/30 to-transparent"></div>
            </div>

            {/* 年份信息 */}
            <div className="text-xs text-gray-500">
              {new Date().getFullYear()} • 专业企业服务 • 值得信赖
            </div>

            {/* 政策链接 */}
            <div className="flex flex-wrap justify-center space-x-6 mt-4">
              <Link href="/privacy" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                隐私政策
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                服务条款
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

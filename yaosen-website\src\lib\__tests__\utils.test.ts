/**
 * @fileoverview 工具函数测试
 * <AUTHOR>
 * @created 2024-01-15
 */

import {
  cn,
  formatDate,
  debounce,
  throttle,
  deepClone,
  generateId,
  isValidEmail,
  isValidPhone,
  formatFileSize,
  safeJsonParse,
} from '../utils'

describe('工具函数测试', () => {
  describe('cn 函数', () => {
    it('应该正确合并类名', () => {
      expect(cn('class1', 'class2')).toBe('class1 class2')
      expect(cn('class1', undefined, 'class2')).toBe('class1 class2')
      expect(cn('class1', false && 'class2', 'class3')).toBe('class1 class3')
    })
  })

  describe('formatDate 函数', () => {
    const testDate = new Date('2024-01-15T10:30:00')

    it('应该格式化完整日期', () => {
      const result = formatDate(testDate, 'full')
      expect(result).toContain('2024')
      expect(result).toContain('1月')
      expect(result).toContain('15')
    })

    it('应该格式化短日期', () => {
      const result = formatDate(testDate, 'short')
      expect(result).toContain('2024')
    })

    it('应该格式化时间', () => {
      const result = formatDate(testDate, 'time')
      expect(result).toContain('10:30')
    })
  })

  describe('debounce 函数', () => {
    jest.useFakeTimers()

    it('应该延迟执行函数', () => {
      const mockFn = jest.fn()
      const debouncedFn = debounce(mockFn, 100)

      debouncedFn()
      expect(mockFn).not.toHaveBeenCalled()

      jest.advanceTimersByTime(100)
      expect(mockFn).toHaveBeenCalledTimes(1)
    })

    it('应该取消之前的调用', () => {
      const mockFn = jest.fn()
      const debouncedFn = debounce(mockFn, 100)

      debouncedFn()
      debouncedFn()
      debouncedFn()

      jest.advanceTimersByTime(100)
      expect(mockFn).toHaveBeenCalledTimes(1)
    })

    afterEach(() => {
      jest.clearAllTimers()
    })
  })

  describe('throttle 函数', () => {
    jest.useFakeTimers()

    it('应该限制函数执行频率', () => {
      const mockFn = jest.fn()
      const throttledFn = throttle(mockFn, 100)

      throttledFn()
      throttledFn()
      throttledFn()

      expect(mockFn).toHaveBeenCalledTimes(1)

      jest.advanceTimersByTime(100)
      throttledFn()
      expect(mockFn).toHaveBeenCalledTimes(2)
    })

    afterEach(() => {
      jest.clearAllTimers()
    })
  })

  describe('deepClone 函数', () => {
    it('应该深拷贝对象', () => {
      const original = {
        a: 1,
        b: {
          c: 2,
          d: [3, 4, 5]
        }
      }

      const cloned = deepClone(original)
      
      expect(cloned).toEqual(original)
      expect(cloned).not.toBe(original)
      expect(cloned.b).not.toBe(original.b)
      expect(cloned.b.d).not.toBe(original.b.d)
    })

    it('应该处理基本类型', () => {
      expect(deepClone(null)).toBe(null)
      expect(deepClone(undefined)).toBe(undefined)
      expect(deepClone(42)).toBe(42)
      expect(deepClone('string')).toBe('string')
    })

    it('应该处理日期对象', () => {
      const date = new Date('2024-01-15')
      const cloned = deepClone(date)
      
      expect(cloned).toEqual(date)
      expect(cloned).not.toBe(date)
    })
  })

  describe('generateId 函数', () => {
    it('应该生成指定长度的ID', () => {
      expect(generateId(8)).toHaveLength(8)
      expect(generateId(16)).toHaveLength(16)
    })

    it('应该生成不同的ID', () => {
      const id1 = generateId()
      const id2 = generateId()
      expect(id1).not.toBe(id2)
    })
  })

  describe('isValidEmail 函数', () => {
    it('应该验证有效邮箱', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true)
      expect(isValidEmail('<EMAIL>')).toBe(true)
    })

    it('应该拒绝无效邮箱', () => {
      expect(isValidEmail('invalid-email')).toBe(false)
      expect(isValidEmail('test@')).toBe(false)
      expect(isValidEmail('@example.com')).toBe(false)
    })
  })

  describe('isValidPhone 函数', () => {
    it('应该验证有效手机号', () => {
      expect(isValidPhone('13800138000')).toBe(true)
      expect(isValidPhone('15912345678')).toBe(true)
    })

    it('应该拒绝无效手机号', () => {
      expect(isValidPhone('12800138000')).toBe(false)
      expect(isValidPhone('1380013800')).toBe(false)
      expect(isValidPhone('138001380000')).toBe(false)
    })
  })

  describe('formatFileSize 函数', () => {
    it('应该格式化文件大小', () => {
      expect(formatFileSize(0)).toBe('0 Bytes')
      expect(formatFileSize(1024)).toBe('1 KB')
      expect(formatFileSize(1024 * 1024)).toBe('1 MB')
      expect(formatFileSize(1536)).toBe('1.5 KB')
    })
  })

  describe('safeJsonParse 函数', () => {
    it('应该解析有效JSON', () => {
      const obj = { test: 'value' }
      const json = JSON.stringify(obj)
      expect(safeJsonParse(json, null)).toEqual(obj)
    })

    it('应该返回默认值当JSON无效时', () => {
      const defaultValue = { default: true }
      expect(safeJsonParse('invalid json', defaultValue)).toBe(defaultValue)
    })
  })
})

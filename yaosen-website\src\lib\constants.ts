/**
 * @fileoverview 应用常量定义
 * <AUTHOR>
 * @created 2024-01-15
 * @updated 2024-01-15
 */

// 公司信息常量
export const COMPANY_INFO = {
  MAIN_COMPANY: '安徽追逐科技有限公司',
  TECH_COMPANY: '安徽追逐科技有限公司',
  ESTABLISHED_DATE: '2023年07月27日',
  ADDRESS: '安徽省合肥市蜀山区蜀山经济开发区中国(安徽)自由贸易试验区合肥片区花峰路1201号跨境电商产业园三期3幢G区8层1326号',
  ICP_NUMBER: '皖ICP备**********号-1',
  ICP_LICENSE: '皖B2-20250341',
  COPYRIGHT: 'Copyright © 安徽追逐科技有限公司 版权所有'
} as const

// 联系信息常量
export const CONTACT_INFO = {
  PHONE: '************',
  EMAIL: '<EMAIL>',
  BUSINESS_HOURS: {
    WEEKDAYS: '周一至周五',
    TIME: '9:00 - 18:00'
  }
} as const

// API 相关常量
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
  TIMEOUT: 10000, // 10秒超时
  RETRY_ATTEMPTS: 3
} as const

// 文件上传配置
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  ALLOWED_DOCUMENT_TYPES: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  ALLOWED_FILE_EXTENSIONS: ['jpg', 'jpeg', 'png', 'webp', 'pdf', 'doc', 'docx']
} as const

// 分页配置
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_PAGE_SIZE: 100
} as const

// 表单验证规则
export const VALIDATION_RULES = {
  USERNAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 20,
    PATTERN: /^[a-zA-Z0-9_]+$/
  },
  PASSWORD: {
    MIN_LENGTH: 8,
    MAX_LENGTH: 128,
    PATTERN: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
  },
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  PHONE: {
    PATTERN: /^1[3-9]\d{9}$/
  }
} as const

// 缓存配置
export const CACHE_CONFIG = {
  DEFAULT_TTL: 5 * 60 * 1000, // 5分钟
  LONG_TTL: 60 * 60 * 1000,   // 1小时
  SHORT_TTL: 30 * 1000,       // 30秒
  KEYS: {
    USER_INFO: 'user_info',
    MENU_DATA: 'menu_data',
    SYSTEM_CONFIG: 'system_config'
  }
} as const

// 主题配置
export const THEME_CONFIG = {
  COLORS: {
    PRIMARY: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a'
    },
    SUCCESS: '#10b981',
    WARNING: '#f59e0b',
    ERROR: '#ef4444',
    INFO: '#3b82f6'
  },
  BREAKPOINTS: {
    SM: '640px',
    MD: '768px',
    LG: '1024px',
    XL: '1280px',
    '2XL': '1536px'
  }
} as const

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  SERVER_ERROR: '服务器错误，请稍后重试',
  UNAUTHORIZED: '未授权访问，请先登录',
  FORBIDDEN: '权限不足，无法访问该资源',
  NOT_FOUND: '请求的资源不存在',
  VALIDATION_ERROR: '输入数据格式不正确',
  FILE_TOO_LARGE: '文件大小超出限制',
  INVALID_FILE_TYPE: '不支持的文件类型',
  UPLOAD_FAILED: '文件上传失败'
} as const

// 成功消息
export const SUCCESS_MESSAGES = {
  SAVE_SUCCESS: '保存成功',
  DELETE_SUCCESS: '删除成功',
  UPDATE_SUCCESS: '更新成功',
  UPLOAD_SUCCESS: '上传成功',
  SEND_SUCCESS: '发送成功'
} as const

// 路由路径
export const ROUTES = {
  HOME: '/',
  ABOUT: '/about',
  SERVICES: '/services',
  CASES: '/cases',
  CONTACT: '/contact',
  PRIVACY: '/privacy',
  TERMS: '/terms'
} as const

// 本地存储键名
export const STORAGE_KEYS = {
  USER_TOKEN: 'user_token',
  USER_PREFERENCES: 'user_preferences',
  THEME: 'theme',
  LANGUAGE: 'language'
} as const

// 性能监控配置
export const PERFORMANCE_CONFIG = {
  THRESHOLDS: {
    FCP: 1800,  // 首次内容绘制 < 1.8s
    LCP: 2500,  // 最大内容绘制 < 2.5s
    FID: 100,   // 首次输入延迟 < 100ms
    CLS: 0.1,   // 累积布局偏移 < 0.1
    TTI: 3800   // 可交互时间 < 3.8s
  }
} as const

// 业务状态枚举
export const BUSINESS_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PENDING: 'pending',
  SUSPENDED: 'suspended',
  DELETED: 'deleted'
} as const

// 用户角色枚举
export const USER_ROLES = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  EMPLOYEE: 'employee',
  GUEST: 'guest'
} as const

// 权限枚举
export const PERMISSIONS = {
  READ: 'read',
  WRITE: 'write',
  DELETE: 'delete',
  ADMIN: 'admin'
} as const

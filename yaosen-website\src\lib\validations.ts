/**
 * @fileoverview 数据验证规则定义
 * <AUTHOR>
 * @created 2024-01-15
 * @updated 2024-01-15
 */

import { z } from 'zod'
import { VALIDATION_RULES } from './constants'

// 基础验证规则
export const baseValidations = {
  // 用户名验证
  username: z.string()
    .min(VALIDATION_RULES.USERNAME.MIN_LENGTH, `用户名至少${VALIDATION_RULES.USERNAME.MIN_LENGTH}个字符`)
    .max(VALIDATION_RULES.USERNAME.MAX_LENGTH, `用户名最多${VALIDATION_RULES.USERNAME.MAX_LENGTH}个字符`)
    .regex(VALIDATION_RULES.USERNAME.PATTERN, '用户名只能包含字母、数字和下划线'),

  // 邮箱验证
  email: z.string()
    .email('请输入有效的邮箱地址')
    .min(1, '邮箱地址不能为空'),

  // 手机号验证
  phone: z.string()
    .regex(VALIDATION_RULES.PHONE.PATTERN, '请输入有效的手机号码'),

  // 密码验证
  password: z.string()
    .min(VALIDATION_RULES.PASSWORD.MIN_LENGTH, `密码至少${VALIDATION_RULES.PASSWORD.MIN_LENGTH}个字符`)
    .max(VALIDATION_RULES.PASSWORD.MAX_LENGTH, `密码最多${VALIDATION_RULES.PASSWORD.MAX_LENGTH}个字符`)
    .regex(VALIDATION_RULES.PASSWORD.PATTERN, '密码必须包含大小写字母、数字和特殊字符'),

  // 必填字符串
  requiredString: (fieldName: string) => z.string()
    .min(1, `${fieldName}不能为空`)
    .trim(),

  // 可选字符串
  optionalString: z.string().optional(),

  // URL验证
  url: z.string().url('请输入有效的URL地址'),

  // 数字验证
  positiveNumber: z.number().positive('必须为正数'),
  nonNegativeNumber: z.number().min(0, '不能为负数'),

  // 日期验证
  date: z.date(),
  dateString: z.string().datetime('请输入有效的日期时间格式'),

  // UUID验证
  uuid: z.string().uuid('请输入有效的UUID格式')
}

// 用户相关验证模式
export const userSchemas = {
  // 创建用户
  createUser: z.object({
    username: baseValidations.username,
    email: baseValidations.email,
    phone: baseValidations.phone,
    password: baseValidations.password,
    confirmPassword: z.string(),
    firstName: baseValidations.requiredString('姓'),
    lastName: baseValidations.requiredString('名'),
    departmentId: baseValidations.uuid.optional(),
    roleId: baseValidations.uuid
  }).refine((data) => data.password === data.confirmPassword, {
    message: '两次输入的密码不一致',
    path: ['confirmPassword']
  }),

  // 更新用户
  updateUser: z.object({
    id: baseValidations.uuid,
    username: baseValidations.username.optional(),
    email: baseValidations.email.optional(),
    phone: baseValidations.phone.optional(),
    firstName: baseValidations.requiredString('姓').optional(),
    lastName: baseValidations.requiredString('名').optional(),
    departmentId: baseValidations.uuid.optional(),
    roleId: baseValidations.uuid.optional()
  }),

  // 用户登录
  login: z.object({
    username: baseValidations.requiredString('用户名'),
    password: baseValidations.requiredString('密码')
  }),

  // 修改密码
  changePassword: z.object({
    currentPassword: baseValidations.requiredString('当前密码'),
    newPassword: baseValidations.password,
    confirmPassword: z.string()
  }).refine((data) => data.newPassword === data.confirmPassword, {
    message: '两次输入的新密码不一致',
    path: ['confirmPassword']
  })
}

// 企业相关验证模式
export const enterpriseSchemas = {
  // 创建企业
  createEnterprise: z.object({
    name: baseValidations.requiredString('企业名称'),
    industry: baseValidations.requiredString('行业'),
    scale: z.enum(['small', 'medium', 'large'], {
      errorMap: () => ({ message: '请选择企业规模' })
    }),
    address: baseValidations.requiredString('地址'),
    contactPerson: baseValidations.requiredString('联系人'),
    contactPhone: baseValidations.phone,
    contactEmail: baseValidations.email,
    description: baseValidations.optionalString,
    website: baseValidations.url.optional()
  }),

  // 更新企业
  updateEnterprise: z.object({
    id: baseValidations.uuid,
    name: baseValidations.requiredString('企业名称').optional(),
    industry: baseValidations.requiredString('行业').optional(),
    scale: z.enum(['small', 'medium', 'large']).optional(),
    address: baseValidations.requiredString('地址').optional(),
    contactPerson: baseValidations.requiredString('联系人').optional(),
    contactPhone: baseValidations.phone.optional(),
    contactEmail: baseValidations.email.optional(),
    description: baseValidations.optionalString,
    website: baseValidations.url.optional()
  })
}

// 联系表单验证模式
export const contactSchemas = {
  // 联系我们表单
  contactForm: z.object({
    name: baseValidations.requiredString('姓名'),
    email: baseValidations.email,
    phone: baseValidations.phone.optional(),
    company: baseValidations.optionalString,
    subject: baseValidations.requiredString('主题'),
    message: baseValidations.requiredString('留言内容')
      .min(10, '留言内容至少10个字符')
      .max(1000, '留言内容最多1000个字符')
  }),

  // 服务咨询表单
  serviceInquiry: z.object({
    name: baseValidations.requiredString('姓名'),
    email: baseValidations.email,
    phone: baseValidations.phone,
    company: baseValidations.requiredString('公司名称'),
    serviceType: z.enum(['consulting', 'technical', 'data', 'engineering', 'sales'], {
      errorMap: () => ({ message: '请选择服务类型' })
    }),
    budget: z.enum(['under-10k', '10k-50k', '50k-100k', 'over-100k'], {
      errorMap: () => ({ message: '请选择预算范围' })
    }),
    timeline: baseValidations.requiredString('项目时间'),
    requirements: baseValidations.requiredString('需求描述')
      .min(20, '需求描述至少20个字符')
      .max(2000, '需求描述最多2000个字符')
  })
}

// 文件上传验证模式
export const fileSchemas = {
  // 图片上传
  imageUpload: z.object({
    file: z.instanceof(File)
      .refine((file) => file.size <= 10 * 1024 * 1024, '图片大小不能超过10MB')
      .refine(
        (file) => ['image/jpeg', 'image/png', 'image/webp'].includes(file.type),
        '只支持 JPG、PNG、WebP 格式的图片'
      ),
    alt: baseValidations.optionalString
  }),

  // 文档上传
  documentUpload: z.object({
    file: z.instanceof(File)
      .refine((file) => file.size <= 50 * 1024 * 1024, '文档大小不能超过50MB')
      .refine(
        (file) => [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ].includes(file.type),
        '只支持 PDF、DOC、DOCX 格式的文档'
      ),
    description: baseValidations.optionalString
  })
}

// 搜索和筛选验证模式
export const searchSchemas = {
  // 通用搜索
  search: z.object({
    query: z.string().max(100, '搜索关键词最多100个字符').optional(),
    page: z.number().min(1, '页码必须大于0').default(1),
    pageSize: z.number().min(1).max(100, '每页数量不能超过100').default(10),
    sortBy: baseValidations.optionalString,
    sortOrder: z.enum(['asc', 'desc']).default('desc')
  }),

  // 日期范围搜索
  dateRangeSearch: z.object({
    startDate: baseValidations.dateString.optional(),
    endDate: baseValidations.dateString.optional()
  }).refine((data) => {
    if (data.startDate && data.endDate) {
      return new Date(data.startDate) <= new Date(data.endDate)
    }
    return true
  }, {
    message: '开始日期不能晚于结束日期',
    path: ['endDate']
  })
}

// 类型推断辅助
export type CreateUserInput = z.infer<typeof userSchemas.createUser>
export type UpdateUserInput = z.infer<typeof userSchemas.updateUser>
export type LoginInput = z.infer<typeof userSchemas.login>
export type ChangePasswordInput = z.infer<typeof userSchemas.changePassword>

export type CreateEnterpriseInput = z.infer<typeof enterpriseSchemas.createEnterprise>
export type UpdateEnterpriseInput = z.infer<typeof enterpriseSchemas.updateEnterprise>

export type ContactFormInput = z.infer<typeof contactSchemas.contactForm>
export type ServiceInquiryInput = z.infer<typeof contactSchemas.serviceInquiry>

export type SearchInput = z.infer<typeof searchSchemas.search>
export type DateRangeSearchInput = z.infer<typeof searchSchemas.dateRangeSearch>

/**
 * @fileoverview 全局类型定义
 * <AUTHOR>
 * @created 2024-01-15
 * @updated 2024-01-15
 */

// 基础类型定义
export interface BaseEntity {
  id: string
  createdAt: Date
  updatedAt: Date
  createdBy?: string
  updatedBy?: string
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  code?: number
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// 用户相关类型
export interface User extends BaseEntity {
  username: string
  email: string
  phone?: string
  firstName: string
  lastName: string
  avatar?: string
  status: 'active' | 'inactive' | 'suspended'
  role: UserRole
  department?: Department
  lastLoginAt?: Date
  emailVerified: boolean
  phoneVerified: boolean
}

export interface UserRole extends BaseEntity {
  name: string
  description?: string
  permissions: Permission[]
  isSystem: boolean
}

export interface Permission extends BaseEntity {
  resource: string
  action: string
  scope: string
  description?: string
}

// 企业相关类型
export interface Enterprise extends BaseEntity {
  name: string
  industry: string
  scale: 'small' | 'medium' | 'large'
  address: string
  contactPerson: string
  contactPhone: string
  contactEmail: string
  description?: string
  website?: string
  logo?: string
  status: 'active' | 'inactive' | 'pending' | 'suspended'
  departments: Department[]
  employees: User[]
}

export interface Department extends BaseEntity {
  name: string
  description?: string
  parentId?: string
  parent?: Department
  children?: Department[]
  manager?: User
  employees: User[]
  enterpriseId: string
  enterprise: Enterprise
}

// 服务相关类型
export interface Service extends BaseEntity {
  name: string
  category: 'consulting' | 'technical' | 'data' | 'engineering' | 'sales'
  description: string
  features: string[]
  price?: {
    type: 'fixed' | 'hourly' | 'project'
    amount: number
    currency: string
  }
  duration?: string
  status: 'active' | 'inactive'
  image?: string
}

// 案例相关类型
export interface Case extends BaseEntity {
  title: string
  client: string
  industry: string
  services: string[]
  description: string
  challenge: string
  solution: string
  results: string[]
  images: string[]
  tags: string[]
  featured: boolean
  status: 'published' | 'draft' | 'archived'
}

// 联系表单类型
export interface ContactSubmission extends BaseEntity {
  name: string
  email: string
  phone?: string
  company?: string
  subject: string
  message: string
  status: 'new' | 'processing' | 'resolved' | 'closed'
  assignedTo?: string
  notes?: string
}

export interface ServiceInquiry extends BaseEntity {
  name: string
  email: string
  phone: string
  company: string
  serviceType: 'consulting' | 'technical' | 'data' | 'engineering' | 'sales'
  budget: 'under-10k' | '10k-50k' | '50k-100k' | 'over-100k'
  timeline: string
  requirements: string
  status: 'new' | 'quoted' | 'negotiating' | 'accepted' | 'rejected'
  assignedTo?: string
  quote?: {
    amount: number
    currency: string
    validUntil: Date
    notes?: string
  }
}

// 文件相关类型
export interface FileUpload {
  id: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
  uploadedBy: string
  uploadedAt: Date
  metadata?: Record<string, any>
}

// 系统配置类型
export interface SystemConfig {
  siteName: string
  siteDescription: string
  siteUrl: string
  contactEmail: string
  contactPhone: string
  address: string
  socialMedia: {
    wechat?: string
    weibo?: string
    linkedin?: string
  }
  seo: {
    keywords: string[]
    ogImage?: string
  }
  features: {
    userRegistration: boolean
    emailNotifications: boolean
    smsNotifications: boolean
  }
}

// 导航菜单类型
export interface MenuItem {
  id: string
  label: string
  href: string
  icon?: string
  children?: MenuItem[]
  external?: boolean
  requireAuth?: boolean
  permissions?: string[]
}

// 表单相关类型
export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'checkbox' | 'radio' | 'file'
  required?: boolean
  placeholder?: string
  options?: { label: string; value: string }[]
  validation?: {
    min?: number
    max?: number
    pattern?: RegExp
    custom?: (value: any) => boolean | string
  }
}

export interface FormState {
  values: Record<string, any>
  errors: Record<string, string>
  touched: Record<string, boolean>
  isSubmitting: boolean
  isValid: boolean
}

// 搜索和筛选类型
export interface SearchParams {
  query?: string
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  filters?: Record<string, any>
}

export interface FilterOption {
  label: string
  value: string
  count?: number
}

// 通知类型
export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: Date
  read: boolean
  actions?: {
    label: string
    action: () => void
  }[]
}

// 主题类型
export interface Theme {
  name: string
  colors: {
    primary: string
    secondary: string
    success: string
    warning: string
    error: string
    info: string
    background: string
    surface: string
    text: string
  }
  fonts: {
    primary: string
    secondary: string
  }
  spacing: Record<string, string>
  breakpoints: Record<string, string>
}

// 错误类型
export interface AppError {
  code: string
  message: string
  details?: any
  timestamp: Date
  userId?: string
  requestId?: string
}

// 性能监控类型
export interface PerformanceMetrics {
  pageLoadTime: number
  firstContentfulPaint: number
  largestContentfulPaint: number
  firstInputDelay: number
  cumulativeLayoutShift: number
  timeToInteractive: number
}

// 分析数据类型
export interface AnalyticsEvent {
  eventName: string
  properties: Record<string, any>
  userId?: string
  sessionId: string
  timestamp: Date
  page: string
  userAgent: string
}

// 工具类型
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

// 组件 Props 类型
export interface ComponentProps {
  className?: string
  children?: React.ReactNode
  'data-testid'?: string
}

export interface ButtonProps extends ComponentProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  loading?: boolean
  disabled?: boolean
  onClick?: () => void
  type?: 'button' | 'submit' | 'reset'
}

export interface InputProps extends ComponentProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url'
  placeholder?: string
  value?: string
  defaultValue?: string
  onChange?: (value: string) => void
  onBlur?: () => void
  onFocus?: () => void
  error?: string
  disabled?: boolean
  required?: boolean
}

export interface ModalProps extends ComponentProps {
  open: boolean
  onClose: () => void
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  closable?: boolean
  maskClosable?: boolean
}

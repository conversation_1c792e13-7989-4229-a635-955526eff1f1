@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 耀森网站本地开发快速启动脚本 (Windows版本)
:: 一键启动本地开发环境

echo.
echo ==================================================
echo     耀森企业管理有限公司 - 本地开发启动器
echo ==================================================
echo.
echo 快速启动本地开发环境
echo.

:: 检查Node.js环境
echo [检查] 检查Node.js环境...
where node >nul 2>&1
if errorlevel 1 (
    echo [错误] Node.js 未安装，请先安装 Node.js 18+
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

for /f "tokens=1" %%i in ('node -v') do set NODE_VERSION=%%i
echo [成功] Node.js 版本: !NODE_VERSION!

where npm >nul 2>&1
if errorlevel 1 (
    echo [错误] npm 未安装
    pause
    exit /b 1
)

for /f "tokens=1" %%i in ('npm -v') do set NPM_VERSION=%%i
echo [成功] npm 版本: !NPM_VERSION!

:: 检查项目依赖
echo.
echo [检查] 检查项目依赖...

if not exist "package.json" (
    echo [错误] 未找到 package.json 文件，请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

if not exist "node_modules" (
    echo [信息] node_modules 不存在，将自动安装依赖...
    npm install
) else (
    echo [成功] 依赖已安装
)

:: 检查环境变量配置
echo.
echo [检查] 检查环境变量配置...

if not exist ".env.local" (
    if not exist ".env" (
        echo [警告] 未找到环境变量文件
        
        if exist "scripts\security-setup.sh" (
            echo [信息] 请运行安全初始化脚本: .\scripts\security-setup.sh
        ) else if exist ".env.example" (
            echo [信息] 复制环境变量模板...
            copy ".env.example" ".env.local" >nul
            echo [警告] 请编辑 .env.local 文件配置必要的环境变量
        ) else (
            echo [警告] 未找到环境变量模板，将使用默认配置
        )
    ) else (
        echo [成功] 环境变量配置已存在
    )
) else (
    echo [成功] 环境变量配置已存在
)

:: 检查端口占用
echo.
echo [检查] 检查端口 3000 占用情况...
netstat -an | find "3000" | find "LISTENING" >nul 2>&1
if not errorlevel 1 (
    echo [警告] 端口 3000 已被占用
    set /p KILL_PROCESS="是否要使用其他端口? [y/N]: "
    if /i "!KILL_PROCESS!"=="y" (
        echo [信息] 将使用端口 3001
        set USE_PORT=3001
    ) else (
        set USE_PORT=3000
    )
) else (
    echo [成功] 端口 3000 可用
    set USE_PORT=3000
)

:: 显示开发信息
echo.
echo ==================================================
echo               开发环境启动成功！
echo ==================================================
echo.
echo [访问信息]
echo   🌐 本地地址: http://localhost:!USE_PORT!
echo   🌐 网络地址: http://%COMPUTERNAME%:!USE_PORT!
echo.
echo [开发命令]
echo   📝 代码检查: npm run lint
echo   🧪 运行测试: npm run test
echo   🔍 类型检查: npm run type-check
echo   🏗️  构建项目: npm run build
echo.
echo [有用的快捷键]
echo   Ctrl+C: 停止开发服务器
echo   r + Enter: 重启开发服务器
echo.
echo [开发提示]
echo   • 修改代码后会自动热重载
echo   • 查看浏览器控制台获取详细错误信息
echo   • 使用 VS Code 获得最佳开发体验
echo.

:: 启动开发服务器
echo [启动] 启动开发服务器...
echo.

if "!USE_PORT!"=="3001" (
    npm run dev -- --port 3001
) else (
    npm run dev
)

pause

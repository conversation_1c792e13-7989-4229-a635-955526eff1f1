#!/bin/bash

# 耀森网站本地开发快速启动脚本
# 一键启动本地开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "    耀森企业管理有限公司 - 本地开发启动器"
    echo "=================================================="
    echo -e "${NC}"
    echo "快速启动本地开发环境"
    echo ""
}

# 检查Node.js环境
check_node() {
    log_step "检查Node.js环境..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js 18+"
        echo "下载地址: https://nodejs.org/"
        exit 1
    fi
    
    local node_version=$(node -v | cut -d'v' -f2)
    local major_version=$(echo $node_version | cut -d'.' -f1)
    
    if [ "$major_version" -lt 18 ]; then
        log_error "Node.js 版本过低 ($node_version)，需要 18.0.0 或更高版本"
        exit 1
    fi
    
    log_success "Node.js 版本: $node_version"
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    local npm_version=$(npm -v)
    log_success "npm 版本: $npm_version"
}

# 检查项目依赖
check_dependencies() {
    log_step "检查项目依赖..."
    
    if [ ! -f "package.json" ]; then
        log_error "未找到 package.json 文件，请确保在项目根目录运行此脚本"
        exit 1
    fi
    
    if [ ! -d "node_modules" ]; then
        log_info "node_modules 不存在，将自动安装依赖..."
        npm install
    else
        # 检查 package-lock.json 是否比 node_modules 新
        if [ "package-lock.json" -nt "node_modules" ]; then
            log_info "检测到依赖更新，重新安装..."
            npm install
        else
            log_success "依赖已安装"
        fi
    fi
}

# 检查环境变量配置
check_env_config() {
    log_step "检查环境变量配置..."
    
    if [ ! -f ".env.local" ] && [ ! -f ".env" ]; then
        log_warning "未找到环境变量文件"
        
        if [ -f "scripts/security-setup.sh" ]; then
            log_info "运行安全初始化脚本..."
            ./scripts/security-setup.sh
        elif [ -f ".env.example" ]; then
            log_info "复制环境变量模板..."
            cp .env.example .env.local
            log_warning "请编辑 .env.local 文件配置必要的环境变量"
        else
            log_warning "未找到环境变量模板，将使用默认配置"
        fi
    else
        log_success "环境变量配置已存在"
    fi
}

# 检查端口占用
check_port() {
    local port=${1:-3000}
    log_step "检查端口 $port 占用情况..."
    
    if command -v lsof &> /dev/null; then
        if lsof -ti:$port &> /dev/null; then
            log_warning "端口 $port 已被占用"
            
            read -p "是否要杀死占用端口的进程? [y/N]: " kill_process
            if [[ $kill_process =~ ^[Yy]$ ]]; then
                local pid=$(lsof -ti:$port)
                kill -9 $pid 2>/dev/null || true
                log_success "已杀死进程 $pid"
            else
                log_info "将使用其他可用端口"
                return 1
            fi
        else
            log_success "端口 $port 可用"
        fi
    else
        log_info "无法检查端口占用（lsof 未安装）"
    fi
    
    return 0
}

# 启动开发服务器
start_dev_server() {
    log_step "启动开发服务器..."
    
    # 检查端口
    if ! check_port 3000; then
        log_info "使用其他端口启动..."
        npm run dev -- --port 3001
    else
        npm run dev
    fi
}

# 显示开发信息
show_dev_info() {
    echo ""
    log_success "开发环境启动成功！"
    echo ""
    echo -e "${CYAN}访问信息:${NC}"
    echo "  🌐 本地地址: http://localhost:3000"
    echo "  🌐 网络地址: http://$(hostname -I | awk '{print $1}'):3000"
    echo ""
    echo -e "${CYAN}开发命令:${NC}"
    echo "  📝 代码检查: npm run lint"
    echo "  🧪 运行测试: npm run test"
    echo "  🔍 类型检查: npm run type-check"
    echo "  🏗️  构建项目: npm run build"
    echo ""
    echo -e "${CYAN}有用的快捷键:${NC}"
    echo "  Ctrl+C: 停止开发服务器"
    echo "  r + Enter: 重启开发服务器"
    echo ""
    echo -e "${YELLOW}开发提示:${NC}"
    echo "  • 修改代码后会自动热重载"
    echo "  • 查看浏览器控制台获取详细错误信息"
    echo "  • 使用 VS Code 获得最佳开发体验"
    echo ""
}

# 主函数
main() {
    show_welcome
    check_node
    check_dependencies
    check_env_config
    show_dev_info
    start_dev_server
}

# 错误处理
trap 'echo -e "\n${RED}开发服务器已停止${NC}"' INT TERM

# 运行主函数
main "$@"

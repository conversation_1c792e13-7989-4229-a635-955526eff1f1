@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 耀森网站快速更新脚本 (Windows版本)
:: 官方镜像 + 目录映射架构

echo.
echo ==================================================
echo       耀森网站快速更新工具
echo     ^(官方镜像 + 目录映射架构^)
echo ==================================================
echo.
echo 🚀 快速更新特点:
echo   • 只同步代码文件，无需重建镜像
echo   • 支持增量更新，速度极快
echo   • 自动重启应用，立即生效
echo.

:: 获取服务器信息
if exist ".deploy-config.bat" (
    call .deploy-config.bat
    echo [信息] 从配置文件读取服务器信息
) else (
    echo [输入] 请输入服务器信息:
    set /p SERVER_IP="服务器IP地址: "
    set /p SERVER_USER="SSH用户名 [默认: root]: "
    if "!SERVER_USER!"=="" set SERVER_USER=root
    set /p SERVER_PATH="远程部署路径 [默认: /opt/yaosen-website]: "
    if "!SERVER_PATH!"=="" set SERVER_PATH=/opt/yaosen-website
    
    :: 保存配置
    echo set SERVER_IP=!SERVER_IP! > .deploy-config.bat
    echo set SERVER_USER=!SERVER_USER! >> .deploy-config.bat
    echo set SERVER_PATH=!SERVER_PATH! >> .deploy-config.bat
)

echo.
echo [信息] 服务器信息:
echo   服务器: !SERVER_USER!@!SERVER_IP!
echo   路径: !SERVER_PATH!
echo.

:: 测试连接
echo [测试] 测试服务器连接...
ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=yes !SERVER_USER!@!SERVER_IP! "echo 'Connection test successful'" >nul 2>&1
if errorlevel 1 (
    echo [错误] 无法连接到服务器
    pause
    exit /b 1
)
echo [成功] 服务器连接正常

:: 同步代码文件
echo.
echo [同步] 同步代码文件到服务器...
echo [信息] 准备同步项目文件...

set /p CONFIRM="确认同步文件到服务器? [Y/n]: "
if /i "!CONFIRM!"=="n" (
    echo [取消] 同步已取消
    pause
    exit /b 0
)

echo [信息] 开始同步文件...

:: 使用scp同步主要文件
echo 同步源代码...
scp -r -o StrictHostKeyChecking=yes src !SERVER_USER!@!SERVER_IP!:!SERVER_PATH!/ >nul 2>&1

echo 同步配置文件...
scp -o StrictHostKeyChecking=yes package.json !SERVER_USER!@!SERVER_IP!:!SERVER_PATH!/ >nul 2>&1
scp -o StrictHostKeyChecking=yes next.config.js !SERVER_USER!@!SERVER_IP!:!SERVER_PATH!/ >nul 2>&1
scp -o StrictHostKeyChecking=yes tailwind.config.js !SERVER_USER!@!SERVER_IP!:!SERVER_PATH!/ >nul 2>&1
scp -o StrictHostKeyChecking=yes tsconfig.json !SERVER_USER!@!SERVER_IP!:!SERVER_PATH!/ >nul 2>&1

echo 同步公共资源...
scp -r -o StrictHostKeyChecking=yes public !SERVER_USER!@!SERVER_IP!:!SERVER_PATH!/ >nul 2>&1

echo 同步部署脚本...
scp -o StrictHostKeyChecking=yes docker-compose.yml !SERVER_USER!@!SERVER_IP!:!SERVER_PATH!/ >nul 2>&1
scp -o StrictHostKeyChecking=yes deploy.sh !SERVER_USER!@!SERVER_IP!:!SERVER_PATH!/ >nul 2>&1

echo [成功] 文件同步完成

:: 重启应用
echo.
echo [重启] 重启应用...
ssh -o StrictHostKeyChecking=yes !SERVER_USER!@!SERVER_IP! "
    cd !SERVER_PATH!
    
    # 检查是否有运行中的容器
    if docker-compose ps | grep -q 'yaosen-prod.*Up'; then
        echo '重启生产容器...'
        docker-compose restart yaosen-prod
        
        # 等待容器启动
        sleep 5
        
        # 检查容器状态
        if docker-compose ps | grep -q 'yaosen-prod.*Up'; then
            echo '✅ 应用重启成功'
        else
            echo '❌ 应用重启失败'
            exit 1
        fi
    else
        echo '⚠️  生产容器未运行，启动容器...'
        docker-compose --profile prod up -d
        sleep 10
        
        if docker-compose ps | grep -q 'yaosen-prod.*Up'; then
            echo '✅ 应用启动成功'
        else
            echo '❌ 应用启动失败'
            exit 1
        fi
    fi
"

if errorlevel 1 (
    echo [错误] 应用重启失败
    pause
    exit /b 1
)

echo [成功] 应用重启完成

:: 显示结果
echo.
echo ==================================================
echo               代码更新完成！
echo ==================================================
echo.
echo [访问信息]
echo   🌐 网站地址: http://!SERVER_IP!
echo.
echo [常用命令]
echo   🔄 快速更新: update-remote.bat
echo   📊 查看状态: ssh !SERVER_USER!@!SERVER_IP! "cd !SERVER_PATH! && docker-compose ps"
echo   📝 查看日志: ssh !SERVER_USER!@!SERVER_IP! "cd !SERVER_PATH! && docker-compose logs -f yaosen-prod"
echo.
echo [提示]
echo   • 下次更新只需运行: update-remote.bat
echo   • 无需重新构建镜像，更新速度极快
echo   • 配置信息已保存到 .deploy-config.bat
echo.

pause

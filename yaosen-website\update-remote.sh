#!/bin/bash

# 耀森网站快速更新脚本 (官方镜像 + 目录映射架构)
# 用于快速更新远程服务器上的代码，无需重建镜像

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "      耀森网站快速更新工具"
    echo "    (官方镜像 + 目录映射架构)"
    echo "=================================================="
    echo -e "${NC}"
    echo "🚀 快速更新特点:"
    echo "  • 只同步代码文件，无需重建镜像"
    echo "  • 支持增量更新，速度极快"
    echo "  • 自动重启应用，立即生效"
    echo ""
}

# 获取服务器信息
get_server_info() {
    # 从环境变量或配置文件读取
    if [ -f ".deploy-config" ]; then
        source .deploy-config
        log_info "从配置文件读取服务器信息"
    fi
    
    # 如果没有配置，提示输入
    if [ -z "$SERVER_IP" ]; then
        echo -e "${YELLOW}请输入服务器信息:${NC}"
        read -p "服务器IP地址: " SERVER_IP
    fi
    
    if [ -z "$SERVER_USER" ]; then
        read -p "SSH用户名 [默认: root]: " SERVER_USER
        SERVER_USER=${SERVER_USER:-root}
    fi
    
    if [ -z "$SERVER_PATH" ]; then
        read -p "远程部署路径 [默认: /opt/yaosen-website]: " SERVER_PATH
        SERVER_PATH=${SERVER_PATH:-/opt/yaosen-website}
    fi
    
    # 保存配置
    cat > .deploy-config << EOF
SERVER_IP=$SERVER_IP
SERVER_USER=$SERVER_USER
SERVER_PATH=$SERVER_PATH
EOF
    
    echo ""
    log_info "服务器信息:"
    echo "  服务器: $SERVER_USER@$SERVER_IP"
    echo "  路径: $SERVER_PATH"
    echo ""
}

# 测试连接
test_connection() {
    log_step "测试服务器连接..."
    
    if ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=yes $SERVER_USER@$SERVER_IP "echo 'Connection test successful'" 2>/dev/null; then
        log_success "服务器连接正常"
    else
        log_error "无法连接到服务器"
        exit 1
    fi
}

# 同步代码文件
sync_code() {
    log_step "同步代码文件到服务器..."
    
    # 显示将要同步的文件
    log_info "准备同步以下文件:"
    rsync -avz --dry-run \
        -e "ssh -o StrictHostKeyChecking=yes" \
        --exclude 'node_modules' \
        --exclude '.next' \
        --exclude '.git' \
        --exclude '*.log' \
        --exclude '.env.local' \
        --exclude '.deploy-config' \
        ./ $SERVER_USER@$SERVER_IP:$SERVER_PATH/ | grep -E '^[^/]*\.(js|ts|tsx|json|md|css|scss)$' | head -10
    
    echo ""
    read -p "确认同步这些文件? [Y/n]: " CONFIRM
    if [[ $CONFIRM =~ ^[Nn]$ ]]; then
        log_warning "同步已取消"
        exit 0
    fi
    
    # 执行同步
    log_info "开始同步文件..."
    rsync -avz --progress \
        -e "ssh -o StrictHostKeyChecking=yes" \
        --exclude 'node_modules' \
        --exclude '.next' \
        --exclude '.git' \
        --exclude '*.log' \
        --exclude '.env.local' \
        --exclude '.deploy-config' \
        ./ $SERVER_USER@$SERVER_IP:$SERVER_PATH/
    
    log_success "文件同步完成"
}

# 重启应用
restart_application() {
    log_step "重启应用..."
    
    ssh -o StrictHostKeyChecking=yes $SERVER_USER@$SERVER_IP "
        cd $SERVER_PATH
        
        # 检查是否有运行中的容器
        if docker-compose ps | grep -q 'yaosen-prod.*Up'; then
            echo '重启生产容器...'
            docker-compose restart yaosen-prod
            
            # 等待容器启动
            sleep 5
            
            # 检查容器状态
            if docker-compose ps | grep -q 'yaosen-prod.*Up'; then
                echo '✅ 应用重启成功'
            else
                echo '❌ 应用重启失败'
                exit 1
            fi
        else
            echo '⚠️  生产容器未运行，启动容器...'
            docker-compose --profile prod up -d
            sleep 10
            
            if docker-compose ps | grep -q 'yaosen-prod.*Up'; then
                echo '✅ 应用启动成功'
            else
                echo '❌ 应用启动失败'
                exit 1
            fi
        fi
    "
    
    log_success "应用重启完成"
}

# 获取服务器状态
get_server_status() {
    log_step "获取服务器状态..."
    
    ssh -o StrictHostKeyChecking=yes $SERVER_USER@$SERVER_IP "
        cd $SERVER_PATH
        
        echo '📊 容器状态:'
        docker-compose ps
        
        echo ''
        echo '💾 磁盘使用:'
        df -h $SERVER_PATH
        
        echo ''
        echo '🔄 最近更新:'
        ls -la --time-style='+%Y-%m-%d %H:%M:%S' | head -5
    "
}

# 显示结果
show_result() {
    echo ""
    log_success "代码更新完成！"
    echo ""
    echo -e "${CYAN}访问信息:${NC}"
    echo "  🌐 网站地址: http://$SERVER_IP"
    echo ""
    echo -e "${CYAN}常用命令:${NC}"
    echo "  📊 查看状态: ./update-remote.sh status"
    echo "  🔄 快速更新: ./update-remote.sh"
    echo "  📝 查看日志: ssh $SERVER_USER@$SERVER_IP 'cd $SERVER_PATH && docker-compose logs -f yaosen-prod'"
    echo ""
    echo -e "${YELLOW}提示:${NC}"
    echo "  • 下次更新只需运行: ./update-remote.sh"
    echo "  • 无需重新构建镜像，更新速度极快"
    echo "  • 配置信息已保存到 .deploy-config"
    echo ""
}

# 主函数
main() {
    local action=${1:-update}
    
    case $action in
        "status")
            show_welcome
            get_server_info
            test_connection
            get_server_status
            ;;
        "update"|"")
            show_welcome
            get_server_info
            test_connection
            sync_code
            restart_application
            show_result
            ;;
        "help")
            echo "耀森网站快速更新工具"
            echo ""
            echo "使用方法:"
            echo "  ./update-remote.sh         - 快速更新代码"
            echo "  ./update-remote.sh status  - 查看服务器状态"
            echo "  ./update-remote.sh help    - 显示帮助信息"
            echo ""
            echo "特点:"
            echo "  • 只同步代码文件，速度极快"
            echo "  • 无需重建镜像"
            echo "  • 自动重启应用"
            ;;
        *)
            log_error "未知命令: $action"
            echo "运行 ./update-remote.sh help 查看帮助"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
